'use client';

import { MultiSelect } from '@/components/SurveyBuilder/BuildServeys/questionTypes/DropDown/DropDownMultiSelect';
import FollowUpAnswer from '@/components/SurveyBuilder/BuildServeys/questionTypes/FollowUp/FollowUpAnswer';
import { errorToast } from '@/utils/toast';
import { useEffect, useState } from 'react';
import { isBrowser, isMobile, isTablet } from 'react-device-detect';
import QuestionBox from '../shared/QuestionBox';

export default function DropDown({
  form, //answers list
  currentQuestionIndex,
  formData, //current question details
  setAnswer,
  setForm,
  surveyData,
  selectedLanguage,
  handleNextQuestion,
  totalQuestion,
  displayRequiredAsterisks,
  defaultLanguage,
  deviceType,
}) {
  const [choices, setChoices] = useState([]);
  const [selectedChoices, setSelectedChoices] = useState([]);
  const [triggerNext, setTriggerNext] = useState(false);
  const [isMultipleAnswer, setIsMultipleAnswer] = useState(false);
  const [showFollowUpAnswer, setShowFollowUpAnswer] = useState(false);
  const [selectionType, setSelectionType] = useState('unlimited');
  const [exactChoices, setExactChoices] = useState(0);
  const [choiceRange, setChoiceRange] = useState({ max: 1, min: 0 });
  const [otherQuestionSelected, setOtherQuestionSelected] = useState(false);
  // Define the delimiter constant
  const DELIMITER = '|||';
  const [otherAnswer, setOtherAnswer] = useState('');

  useEffect(() => {
    let data = formData?.questionTypeData?.choices || [];

    if (formData?.questionTypeData?.randomize) {
      // Separate regular and special choices
      const specialChoices = [];
      const regularChoices = [];

      data.forEach((choice, index) => {
        const text = choice.text?.toLowerCase();
        if (
          choice.isOther ||
          text === 'all of the above' ||
          text === 'none of the above'
        ) {
          console.log({ choice }, 'test');
          specialChoices.push({ choice, index }); // Save original index
        } else {
          regularChoices.push(choice);
        }
      });

      // Shuffle regular choices
      for (let i = regularChoices.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [regularChoices[i], regularChoices[j]] = [
          regularChoices[j],
          regularChoices[i],
        ];
      }

      // Rebuild the choices array with shuffled regular choices and original-position special choices
      const shuffledChoices = [];
      let regularIndex = 0;
      for (let i = 0; i < data.length; i++) {
        const special = specialChoices.find((s) => s.index === i);
        if (special) {
          shuffledChoices.push(special.choice);
        } else {
          shuffledChoices.push(regularChoices[regularIndex]);
          regularIndex++;
        }
      }
      console.log({ shuffledChoices }, 'test');

      setChoices(shuffledChoices);
    } else {
      setChoices(data);
    }

    if (formData?.questionTypeData?.default_answer) {
      setSelectedChoices(
        form[currentQuestionIndex]?.answer
          ? form[currentQuestionIndex]?.answer
          : [formData?.questionTypeData?.default_answer_value]
      );
    } else if (
      form[currentQuestionIndex]?.questionId === formData?._id &&
      form[currentQuestionIndex]?.answer === ''
    ) {
      setSelectedChoices([]);
    }

    setIsMultipleAnswer(formData?.questionTypeData?.multiple_answer);
    setSelectionType(formData?.questionTypeData?.selection_limit_type);
    setExactChoices(
      Number(formData?.questionTypeData?.selection_limit_exact_number)
    );
    setChoiceRange({
      max: Number(formData?.questionTypeData?.selection_limit_range_max),
      min: Number(formData?.questionTypeData?.selection_limit_range_min),
    });

    if (form[currentQuestionIndex]?.otherAnswer) {
      setOtherAnswer(form[currentQuestionIndex]?.otherAnswer);
    }
  }, [formData]);

  useEffect(() => {
    if (!selectedChoices?.length && form[currentQuestionIndex]?.answer) {
      // Use the delimiter instead of comma for splitting
      setSelectedChoices(
        form[currentQuestionIndex]?.answer
          ?.split(DELIMITER)
          .filter((ans) => ans !== '')
      );
    }
  }, [form, formData]);

  useEffect(() => {
    if (otherQuestionSelected) {
      setAnswer(selectedChoices?.join(DELIMITER), true);
      // Don't trigger next question when "Other" is selected
      setTriggerNext(false);
    } else {
      setAnswer(selectedChoices?.join(DELIMITER), false);
    }
  }, [selectedChoices]);

  /* trigger next question function automatically */
  useEffect(() => {
    if (
      triggerNext &&
      !formData?.questionTypeData?.multiple_answer &&
      totalQuestion !== currentQuestionIndex + 1 &&
      form?.findIndex((item) => item?.questionId === formData?._id) ===
        currentQuestionIndex &&
      form?.answer !== '' &&
      selectedChoices?.length > 0
      // formData[currentQuestionIndex - 1]?.questionTypeData !== 'Section'
    ) {
      const timer = setTimeout(() => {
        handleNextQuestion({ openThankyou: false });
        setTriggerNext(false);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [triggerNext, handleNextQuestion, form]);

  const handleOtherAnswerChange = (e) => {
    setOtherAnswer(e.target.value);
    // Update the form with the otherAnswer
    const updatedForm = [...form];
    updatedForm[currentQuestionIndex] = {
      ...updatedForm[currentQuestionIndex],
      otherAnswer: e.target.value,
    };
    setForm(updatedForm);
  };

  const handleOptionSelect = (choiceText) => {
    let choicesN = [...selectedChoices];

    // Find the original "Other" choice
    const otherChoice = formData?.questionTypeData?.choices?.find(
      (c) => c.isOther
    );

    // Find the index of the "Other" choice to get its translated version
    const otherChoiceIndex = formData?.questionTypeData?.choices?.findIndex(
      (c) => c.isOther
    );

    // Get the translated "Other" text based on the index
    const translatedOtherText =
      selectedLanguage === defaultLanguage
        ? otherChoice?.text
        : formData?.translations?.[selectedLanguage]?.[0]?.translatedChoices?.[
            otherChoiceIndex
          ]?.text || otherChoice?.text;

    // Check if the selected choice is the "Other" option (either original or translated)
    const isOtherSelectedNow = translatedOtherText === choiceText;

    // Toggle deselection
    if (choicesN.includes(choiceText)) {
      const index = choicesN.findIndex((item) => item === choiceText);
      if (index !== -1) choicesN.splice(index, 1);

      if (isOtherSelectedNow) {
        setOtherAnswer('');
      }
    } else {
      // CHANGED: Improved logic for "None of the above" selection
      // Check if the selected choice is "None of the above"
      if (choiceText === 'None of the above') {
        // If selecting "None of the above", clear all other selections
        choicesN = [choiceText];
      } else {
        // CHANGED: If selecting any other option, remove "None of the above" if it exists
        const noneOfTheAboveIndex = choicesN.findIndex(
          (choice) => choice === 'None of the above'
        );
        if (noneOfTheAboveIndex !== -1) {
          choicesN.splice(noneOfTheAboveIndex, 1);
        }

        // Handle multiple selection with limits
        if (formData?.questionTypeData?.multiple_answer) {
          const type = formData?.questionTypeData?.selection_limit_type;
          if (type === 'unlimited') {
            choicesN.push(choiceText);
          } else if (type === 'exact_number') {
            const limit = Number(
              formData?.questionTypeData?.selection_limit_exact_number
            );
            if (limit > choicesN.length) {
              choicesN.push(choiceText);
            } else {
              errorToast('Cannot select more than ' + limit + ' values');
            }
          } else if (type === 'range') {
            const max = Number(
              formData?.questionTypeData?.selection_limit_range_max
            );
            const min = Number(
              formData?.questionTypeData?.selection_limit_range_min
            );
            if (choicesN.length < max) {
              choicesN.push(choiceText);
            } else {
              errorToast(
                `Cannot select more than ${max} or less than ${min} values`
              );
            }
          } else {
            choicesN.push(choiceText);
          }
        } else {
          choicesN = [choiceText];
        }
      }
    }

    // If the selected choice is "Other" in any language, replace it with the English "Other"
    if (isOtherSelectedNow) {
      // Replace the translated "Other" with the original English "Other" text
      const otherIndex = choicesN.findIndex(
        (choice) => choice === translatedOtherText
      );
      if (otherIndex !== -1) {
        choicesN[otherIndex] = otherChoice.text;
      }
    }

    setSelectedChoices([...choicesN]);

    // Check if "Other" is still selected after update (using both original and translated text)
    const stillHasOther = choicesN.includes(otherChoice?.text);

    setOtherQuestionSelected(stillHasOther);

    if (!stillHasOther && !formData?.questionTypeData?.followUpQuestion) {
      setTriggerNext(true);
    } else {
      setShowFollowUpAnswer(true);
    }

    // Also clear otherAnswer in form state if Other was unselected
    if (!stillHasOther) {
      const updatedForm = [...form];
      updatedForm[currentQuestionIndex] = {
        ...updatedForm[currentQuestionIndex],
        otherAnswer: '',
      };
      setForm(updatedForm);
    }
  };

  const populatePipedChoices = (choice) => {
    const matchingAnswer = form?.find(
      (answer) => answer?.questionId === choice?.question
    );
    const translatedChoices = surveyData?.find(
      (q) => q?._id === choice?.question
    )?.translations?.[selectedLanguage]?.[0]?.translatedChoices;
    const originalAnswers = surveyData?.find((q) => q?._id === choice?.question)
      ?.questionTypeData?.choices;
    const pipedAnswers = originalAnswers?.flatMap((answer, index) => {
      if (answer.isPiped) {
        return populatePipedChoices(answer).map((answer) => {
          return {
            ...answer,
            text: answer.translatedChoice,
          };
        });
      }
      return {
        ...answer,
        text:
          selectedLanguage === defaultLanguage
            ? answer.text
            : translatedChoices?.[index]?.text || answer.text,
      };
    });
    console.log('pipedAnswers', pipedAnswers);
    if (choice.type === 'selected') {
      if (!matchingAnswer || matchingAnswer.answer === '') {
        return null; // Exclude this choice if no matching answer or the answer is an empty string
      }
      // If the piped answer uses the delimiter, we need to handle it properly
      if (matchingAnswer.answer.includes(DELIMITER)) {
        return matchingAnswer.answer.split(DELIMITER).map((answer) => {
          return {
            ...choice,
            translatedChoice: answer,
          };
        });
      } else {
        return {
          ...choice,
          translatedChoice: matchingAnswer.answer,
        };
      }
    } else if (choice.type === 'all') {
      return pipedAnswers?.map((answer) => {
        return {
          ...choice,
          translatedChoice: answer.text,
        };
      });
    } else if (choice.type === 'unselected') {
      return pipedAnswers
        ?.filter((answer) => {
          return !matchingAnswer?.answer?.includes(answer.text);
        })
        ?.map((answer) => {
          return {
            ...choice,
            translatedChoice: answer?.text,
          };
        });
    }
  };
  // translated and piped question choice text added choices array
  const modifiedChoices = choices
    ?.flatMap((choice, index) => {
      let translatedChoice = '';

      if (choice.isPiped) {
        return populatePipedChoices(choice);
      } else {
        translatedChoice =
          selectedLanguage === defaultLanguage
            ? choice.text
            : formData?.translations?.[selectedLanguage]?.[0]
                ?.translatedChoices?.[index]?.text || choice.text;
      }

      return {
        ...choice,
        translatedChoice,
      };
    })
    ?.filter((choice) => choice !== null); // Remove null values from the resulting array
  console.log('Modified choices', modifiedChoices);
  const isStacked = formData?.questionTypeData?.stacked;
  // const isMobile = deviceType == 'mobile';
  // const isTablet = deviceType == 'tablet';
  // const isDesktop = deviceType == 'desktop/laptop';

  console.log({ isMobile, isTablet, isBrowser });

  // Dynamic height calculation based on device and stacked state
  const getCardHeight = () => {
    // if (!isStacked) return '';
    if (isBrowser && !isStacked) return 'h-[254px]';
    if (isMobile && isStacked) return 'h-[268px]';
    if (isMobile && !isStacked) return 'h-[178px]';
    if (isTablet && !isStacked) return 'h-[254px]';
    if (isTablet && isStacked) return 'h-[477px]';
    if (isBrowser) return 'h-[477px]';
    return 'h-[477px]'; // fallback
  };

  const getImageHeight = () => {
    // if (!isStacked) return '';
    if (isBrowser && !isStacked) return 'h-[200px]';
    if (isMobile && isStacked) return 'h-[200px]'; // 268px card - 68px for label/padding
    if (isMobile && !isStacked) return 'h-[97px]';
    if (isTablet) return 'h-[423px]';
    if (isTablet && isStacked) return 'h-[423px]';
    if (isTablet && !isStacked) return 'h-[200px]';
    if (isBrowser) return 'h-[423px]'; // 630px card - 70px for label/padding
    return 'h-[423px]'; // fallback
  };
  console.log({ isMobile, isBrowser, isTablet, isStacked });
  console.log('question data', formData?.questionTypeData);
  return (
    <QuestionBox
      title={
        selectedLanguage === defaultLanguage
          ? formData?.question
          : formData?.translations?.[selectedLanguage]?.[0]?.translatedData
      }
      description={formData?.description}
      displayRequiredAsterisks={displayRequiredAsterisks}
      isRequired={formData?.questionTypeData?.required}
      form={form}
      img={formData?.questionTypeData?.questionImage}
      isMobile={isMobile}
      isBrowser={isBrowser}
      isTablet={isTablet}
      isStacked={isStacked}
    >
      <MultiSelect
        options={modifiedChoices}
        onValueChange={setSelectedChoices}
        defaultValue={selectedChoices}
        placeholder={
          isMultipleAnswer
            ? selectionType === 'unlimited'
              ? 'Choose as many as you like'
              : selectionType === 'exact_number'
              ? `Choose any ${exactChoices}`
              : selectionType === 'range' &&
                `Choose between ${choiceRange.min} and ${choiceRange.max}`
            : 'Select the correct answer'
        }
        variant="inverted"
        animation={2}
        maxCount={10}
        minSelect={0}
        maxSelect={
          isMultipleAnswer
            ? selectionType === 'unlimited'
              ? modifiedChoices.length
              : selectionType === 'exact_number'
              ? exactChoices
              : choiceRange.max
            : 1
        }
        showSelectAll={
          isMultipleAnswer &&
          (selectionType === 'unlimited' ||
            (selectionType === 'exact_number' &&
              exactChoices === modifiedChoices.length) ||
            (selectionType === 'range' &&
              choiceRange.max === modifiedChoices.length))
        }
      />
      <div>
        {formData?.questionTypeData?.followUpQuestion && showFollowUpAnswer && (
          <FollowUpAnswer
            form={form}
            setForm={setForm}
            selectedQuestion={formData}
            surveyData={surveyData}
            currentQuestionIndex={currentQuestionIndex}
            formData={formData}
            defaultLanguage={defaultLanguage}
            selectedLanguage={selectedLanguage}
          />
        )}
      </div>
    </QuestionBox>
  );
}
