'use client';

import PrimaryButton from '@/components/Common/buttons/primary-button';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import useRouteType from '@/hooks/useRouteType';
import api from '@/lib/api';
import { errorToast, successToast } from '@/utils/toast';
import useReportStore from '@/zustand/useReportStore';
import { useMutation } from '@tanstack/react-query';
import { Check, LoaderCircle, Sparkles } from 'lucide-react';
import { useRef, useState } from 'react';
import type { TBoardBlock, TextBlock } from '../board.types';
import ConfirmDeleteSectionModal, {
  BoardActionsDropdownMenu,
  ConfirmDeleteBoardModal,
  ConfirmDuplicateBoardBlockModal,
} from '../board.utils';
import TextBlockEditor from '../TextBlockEditor';
// Import the CSS fixes
import './editor-fixes.css';

const AILoadingOverlay = ({
  isVisible,
  message,
}: {
  isVisible: boolean;
  message: string;
}) => {
  if (!isVisible) return null;

  return (
    <div className="absolute inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-50 rounded-lg">
      <div className="flex flex-col items-center gap-4 p-8">
        <div className="relative">
          <div className="w-12 h-12 border-4 border-[#0FC083] rounded-full animate-spin">
            <div className="absolute top-0 left-0 w-12 h-12 border-4 border-transparent border-t-[#0FC083] rounded-full animate-spin"></div>
          </div>
          <Sparkles className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-5 w-5 text-[#0FC083] animate-pulse" />
        </div>
        <div className="text-center">
          <p className="text-lg font-medium text-gray-800 mb-1">{message}</p>
          <p className="text-sm text-gray-500">
            This may take a few moments...
          </p>
        </div>
      </div>
    </div>
  );
};

const TextBoardBlock = ({
  block,
  boardBlocks,
  setReportBoards,
}: {
  block: TextBlock;
  boardBlocks: TBoardBlock[];
  setReportBoards: any;
}) => {
  const [htmlContent, setHtmlContent] = useState('');
  const [initialContent, setInitialContent] = useState<any>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDuplicateModalOpen, setIsDuplicateModalOpen] = useState(false);
  const [showCustomAI, setShowCustomAI] = useState(false);
  const [aiOutputType, setAiOutputType] = useState<'short' | 'descriptive'>(
    'short'
  );
  const [customPrompt, setCustomPrompt] = useState('');
  const [loadingMessage, setLoadingMessage] = useState('');
  const [aiGeneratedContent, setAiGeneratedContent] = useState<string | null>(
    null
  );

  const { isInsight } = useRouteType();
  const { viewOnly } = useReportStore();
  const blockCardRef = useRef();

  // Get blocks inside this section if it's a section block
  const blocksInSection =
    block?.customChartType === 'section'
      ? boardBlocks.filter((b) => b.parentSection === block._id)
      : [];

  // AI mutations
  const summarizeBoardMutation = useMutation({
    mutationFn: async () => {
      setLoadingMessage('Analyzing and summarizing your board...');
      const res = await api.post('/board/ai/summarize-board', {
        content: htmlContent || initialContent,
        blockId: block._id,
      });
      return res.data;
    },
    onSuccess: (data) => {
      const newContent = data.data.summary;
      // Set AI generated content to trigger editor update
      setAiGeneratedContent(newContent);
      setHtmlContent(newContent);
      setInitialContent(newContent);
      mutateBoardBlock.mutate({
        id: block._id,
        payload: { content: newContent },
      });
      successToast('Board summarized successfully');
      setLoadingMessage('');
    },
    onError: (error: any) => {
      errorToast(error?.response?.data?.message || 'Failed to summarize board');
      setLoadingMessage('');
    },
  });

  const recommendActionsMutation = useMutation({
    mutationFn: async () => {
      setLoadingMessage('Generating actionable recommendations...');
      const res = await api.post('/board/ai/recommend-actions', {
        content: htmlContent || initialContent,
        blockId: block._id,
      });
      return res.data;
    },
    onSuccess: (data) => {
      const newContent = data.data.recommendations;
      // Set AI generated content to trigger editor update
      setAiGeneratedContent(newContent);
      setHtmlContent(newContent);
      setInitialContent(newContent);
      mutateBoardBlock.mutate({
        id: block._id,
        payload: { content: newContent },
      });
      successToast('Actions recommended successfully');
      setLoadingMessage('');
    },
    onError: (error: any) => {
      errorToast(
        error?.response?.data?.message || 'Failed to recommend actions'
      );
      setLoadingMessage('');
    },
  });

  const customAIMutation = useMutation({
    mutationFn: async (data: {
      prompt: string;
      outputType: 'short' | 'descriptive';
    }) => {
      setLoadingMessage(
        `Creating ${data.outputType} content based on your request...`
      );
      const res = await api.post('/board/ai/custom-request', {
        content: htmlContent || initialContent,
        prompt: data.prompt,
        outputType: data.outputType,
        blockId: block._id,
      });
      return res.data;
    },
    onSuccess: (data) => {
      const newContent = data.data.result;
      // Set AI generated content to trigger editor update
      setAiGeneratedContent(newContent);
      setHtmlContent(newContent);
      setInitialContent(newContent);
      mutateBoardBlock.mutate({
        id: block._id,
        payload: { content: newContent },
      });
      setShowCustomAI(false);
      setCustomPrompt('');
      successToast('AI content generated successfully');
      setLoadingMessage('');
    },
    onError: (error: any) => {
      errorToast(
        error?.response?.data?.message || 'Failed to generate AI content'
      );
      setLoadingMessage('');
    },
  });

  // mutations
  const mutateBoardBlock = useMutation({
    mutationFn: async (data: { id: string; payload: Partial<TBoardBlock> }) => {
      if (!data.id) errorToast('Failed to update block');
      const res = await api.patch(`/board/board-blocks/${data?.id}`, {
        ...data?.payload,
      });
      return res.data;
    },
    onSuccess: (data) => {
      const updatedBlock = data?.data;
      successToast('Block updated successfully');
      // Update Zustand store
      // Create updated boardBlocks array
      const updatedBoardBlocks = boardBlocks.map((block) =>
        block._id == updatedBlock._id ? updatedBlock : block
      );
      // Update Zustand store
      setReportBoards(updatedBoardBlocks);
    },
    onError: (error: any) => {
      errorToast(error?.response?.data?.message || 'Failed to update block');
    },
  });

  // UPDATED: Special delete mutation for sections with proper state management
  const deleteBoardBlock = useMutation({
    mutationFn: async (data: { id: string; deleteAllBlocks?: boolean }) => {
      if (!data?.id) errorToast('Failed to delete block');
      // If it's a section block, use the special delete endpoint
      if (block?.customChartType === 'section') {
        const res = await api.delete(
          `/board/board-blocks/section/${data?.id}`,
          {
            data: { deleteAllBlocks: data.deleteAllBlocks || false },
          }
        );
        return res.data;
      } else {
        // Regular block deletion
        const res = await api.delete(`/board/board-blocks/${data?.id}`);
        return res.data;
      }
    },
    onSuccess: (data) => {
      console.log(data, 'delete');
      const deletedId = data.data?._id || block._id;
      // FIXED: Properly update state immediately after deletion
      let updatedBoardBlocks = [...boardBlocks];
      if (block?.customChartType === 'section') {
        if (data.deletedAllBlocks) {
          // Remove section and all its child blocks
          updatedBoardBlocks = updatedBoardBlocks.filter(
            (b) => b._id !== deletedId && b.parentSection !== deletedId
          );
        } else {
          // Remove section but move child blocks to main board
          updatedBoardBlocks = updatedBoardBlocks
            .filter((b) => b._id !== deletedId) // Remove the section
            .map((b) =>
              b.parentSection === deletedId ? { ...b, parentSection: null } : b
            ); // Move children to main board
        }
      } else {
        // Regular block deletion
        updatedBoardBlocks = updatedBoardBlocks.filter(
          (b) => b._id !== deletedId
        );
      }
      setReportBoards(updatedBoardBlocks);
      successToast(data.message || 'Block deleted successfully');
      setIsDeleteModalOpen(false);
    },
    onError: (error: any) => {
      errorToast(error?.response?.data?.message || 'Failed to delete block');
    },
  });

  const duplicateBoardBlock = useMutation({
    mutationFn: async (data: { id: string }) => {
      if (!data.id) errorToast('Failed to duplicate block');
      const res = await api.patch(`/board/board-blocks/duplicate/${data?.id}`);
      return res.data;
    },
    onSuccess: (data) => {
      const updatedBlock = data?.data;
      console.log({ data }, 'updated_duplicated');
      successToast('Block duplicated successfully');
      setIsDuplicateModalOpen(false);
      // Update Zustand store
      // Create updated boardBlocks array
      const updatedBoardBlocks = [...boardBlocks, updatedBlock];
      // Update Zustand store
      setReportBoards(updatedBoardBlocks);
    },
    onError: (error: any) => {
      errorToast(error?.response?.data?.message || 'Failed to duplicate block');
    },
  });

  const handleDelete = () => {
    setIsDeleteModalOpen(true);
  };

  const handleDuplicate = () => {
    setIsDuplicateModalOpen(true);
  };

  // UPDATED: Handle section deletion with options
  const onDeleteConfirm = (deleteAllBlocks = false) => {
    deleteBoardBlock.mutate({
      id: block?._id,
      deleteAllBlocks,
    });
  };

  const onDuplicateConfirm = () => {
    duplicateBoardBlock.mutate({ id: block?._id });
  };

  const onLockViewChange = (isLocked: boolean) => {
    mutateBoardBlock.mutate({
      id: block?._id,
      payload: {
        isOverflowLocked: isLocked,
        scrollTop: isLocked ? block.scrollTop : 0,
      },
    });
  };

  const handleAIAction = (action: 'summarize' | 'recommend' | 'custom') => {
    switch (action) {
      case 'summarize':
        summarizeBoardMutation.mutate();
        break;
      case 'recommend':
        recommendActionsMutation.mutate();
        break;
      case 'custom':
        setShowCustomAI(true);
        break;
    }
  };

  const handleCustomAISubmit = () => {
    if (!customPrompt.trim()) {
      errorToast('Please enter a prompt');
      return;
    }
    customAIMutation.mutate({
      prompt: customPrompt,
      outputType: aiOutputType,
    });
  };

  const handleSuggestionClick = (suggestion: string) => {
    if (suggestion === 'Summarize this Board') {
      handleAIAction('summarize');
    } else if (suggestion === 'Recommend Actions and Next Steps') {
      handleAIAction('recommend');
    }
  };

  const isAnyAILoading =
    summarizeBoardMutation.isLoading ||
    recommendActionsMutation.isLoading ||
    customAIMutation.isLoading;

  return (
    <div ref={blockCardRef as any} className="text-board-block-container">
      {/* AI Loading Overlay */}
      <AILoadingOverlay isVisible={isAnyAILoading} message={loadingMessage} />

      {!viewOnly && !isInsight && !block?.isTemplate && (
        <>
          <BoardActionsDropdownMenu
            type="text"
            onDelete={handleDelete}
            onDuplicate={handleDuplicate}
            blockCardRef={blockCardRef}
            isLocked={block?.isOverflowLocked}
            onLockViewChange={onLockViewChange}
            isInsideSection={!!block?.parentSection}
            parentSectionId={block?.parentSection}
            blockId={block?._id}
          />
          {/* AI Sparkle Dropdown */}
          <div className="absolute top-2 right-12 z-10">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 hover:bg-gray-100"
                  disabled={isAnyAILoading}
                >
                  {isAnyAILoading ? (
                    <LoaderCircle className="h-4 w-4 animate-spin text-[#0FC083]" />
                  ) : (
                    <Sparkles className="h-4 w-4 text-[#0FC083]" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64">
                <DropdownMenuItem
                  onClick={() => handleAIAction('summarize')}
                  className="flex items-center gap-2 cursor-pointer text-[12px]"
                  disabled={isAnyAILoading}
                >
                  <Sparkles className="h-4 w-4 text-[#0FC083]" />
                  Summarize this Board
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleAIAction('recommend')}
                  className="flex items-center gap-2 cursor-pointer text-[12px]"
                  disabled={isAnyAILoading}
                >
                  <Sparkles className="h-4 w-4 text-[#0FC083]" />
                  Recommend Actions and Next Steps
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => handleAIAction('custom')}
                  className="flex items-center gap-2 cursor-pointer text-[12px]"
                  disabled={isAnyAILoading}
                >
                  <Sparkles className="h-4 w-4 text-[#0FC083]" />
                  Custom Request
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </>
      )}

      <div
        onMouseDown={(e) => {
          // Prevent grid dragging when clicking on text content
          const target = e.target as Element;
          if (
            target.closest(
              '.bn-editor, .mantine-Editor-root, [contenteditable="true"], .bn-formatting-toolbar, .bn-side-menu'
            )
          ) {
            e.stopPropagation();
          }
        }}
        onFocus={(e) => {
          // Prevent grid interactions when text editor is focused
          const target = e.target as Element;
          if (
            target.closest(
              '.bn-editor, .mantine-Editor-root, [contenteditable="true"]'
            )
          ) {
            e.stopPropagation();
          }
        }}
        className="text-editor-wrapper text-content-editable"
        onMouseUp={(e) => e.stopPropagation()}
        onClick={(e) => e.stopPropagation()}
        style={{ pointerEvents: 'auto', userSelect: 'text' }}
      >
        <TextBlockEditor
          block={block}
          htmlContent={htmlContent}
          setHtmlContent={setHtmlContent}
          initialContent={initialContent}
          setInitialContent={setInitialContent}
          viewOnly={viewOnly}
          isInsight={isInsight}
          editMode={true}
          aiGeneratedContent={aiGeneratedContent}
          setAiGeneratedContent={setAiGeneratedContent}
        />
      </div>

      {/* Custom AI Interface */}
      {showCustomAI && !viewOnly && !isInsight && (
        <div
          className="mt-4 p-4 relative"
          onMouseDown={(e) => e.stopPropagation()}
          onClick={(e) => e.stopPropagation()}
          style={{ pointerEvents: 'auto' }}
        >
          <div className="mb-4 bg-[#F1F5F9] p-3 rounded-md">
            <div className="flex items-center gap-2 mb-4">
              <Sparkles className="h-4 w-4 text-[#0FC083]" />
              <span className="text-sm font-medium text-gray-700">
                Generate with AI
              </span>
              <div className="ml-auto flex items-center gap-2">
                <Button
                  variant={aiOutputType === 'short' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setAiOutputType('short')}
                  className={
                    aiOutputType === 'short'
                      ? 'bg-gray-800 text-white hover:text-white'
                      : 'bg-white text-gray-700 border-gray-300'
                  }
                >
                  Short
                </Button>
                <Button
                  variant={
                    aiOutputType === 'descriptive' ? 'default' : 'outline'
                  }
                  size="sm"
                  onClick={() => setAiOutputType('descriptive')}
                  className={
                    aiOutputType === 'descriptive'
                      ? 'bg-gray-800 text-white hover:text-white'
                      : 'bg-white text-gray-700 border-gray-300'
                  }
                >
                  Descriptive
                </Button>
              </div>
            </div>
            <Input
              placeholder="Input your prompt"
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              className="mb-4 bg-white"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleCustomAISubmit();
                }
              }}
            />
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-500">Suggestions</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSuggestionClick('Summarize this Board')}
                className="text-sm text-gray-600 hover:text-gray-800 bg-[#F1F5F9] rounded-full"
                disabled={customAIMutation.isLoading}
              >
                Summarize this Board
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() =>
                  handleSuggestionClick('Recommend Actions and Next Steps')
                }
                className="text-sm text-gray-600 hover:text-gray-800 bg-[#F1F5F9] rounded-full"
                disabled={customAIMutation.isLoading}
              >
                Recommend Actions and Next Steps
              </Button>
            </div>
            {/* Close button */}
            <div className=" flex items-center gap-2">
              <Button
                size="sm"
                onClick={() => {
                  setShowCustomAI(false);
                  setCustomPrompt('');
                }}
                className="bg-red-500 hover:bg-red-600 text-white hover:text-white"
              >
                Close
              </Button>
              <Button
                variant="success"
                size="sm"
                onClick={handleCustomAISubmit}
                disabled={customAIMutation.isLoading || !customPrompt.trim()}
                className="bg-[#0FC083] hover:bg-[#0FC083] text-white"
              >
                {customAIMutation.isLoading ? (
                  <>
                    <LoaderCircle className="mr-2 h-4 w-4 animate-spin" />
                    Generating
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Save
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* save button */}
      {!viewOnly &&
        !isInsight &&
        htmlContent !== initialContent &&
        !showCustomAI && (
          <div className="flex justify-end items-center gap-2 p-3 rounded-b-xl">
            <PrimaryButton
              size="middle"
              onClick={(e) => {
                e.stopPropagation();
                if (htmlContent) {
                  if (htmlContent !== initialContent) {
                    setInitialContent(htmlContent);
                  }
                  mutateBoardBlock.mutate({
                    id: block?._id,
                    payload: {
                      content: htmlContent,
                    },
                  });
                }
              }}
              className=" flex items-center"
              disabled={mutateBoardBlock.isLoading}
            >
              {mutateBoardBlock.isLoading ? (
                <>
                  <LoaderCircle className="mr-2 h-4 w-4 animate-spin" /> Saving
                </>
              ) : (
                <>
                  <Check className="mr-2 h-4 w-4" /> Save
                </>
              )}
            </PrimaryButton>
          </div>
        )}

      {/* modals */}
      {/* UPDATED: Use special section delete modal for section blocks */}
      {block?.customChartType === 'section' ? (
        <ConfirmDeleteSectionModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={onDeleteConfirm}
          isLoading={deleteBoardBlock.isLoading}
          sectionTitle={block?.customChartConfig?.title || 'Section'}
          blockCount={blocksInSection.length}
        />
      ) : (
        <ConfirmDeleteBoardModal
          isOpen={isDeleteModalOpen}
          onClose={() => setIsDeleteModalOpen(false)}
          onConfirm={() => onDeleteConfirm(false)}
          isLoading={deleteBoardBlock.isLoading}
        />
      )}

      <ConfirmDuplicateBoardBlockModal
        isOpen={isDuplicateModalOpen}
        onClose={() => setIsDuplicateModalOpen(false)}
        onConfirm={onDuplicateConfirm}
        isLoading={duplicateBoardBlock.isLoading}
      />
    </div>
  );
};

export default TextBoardBlock;
