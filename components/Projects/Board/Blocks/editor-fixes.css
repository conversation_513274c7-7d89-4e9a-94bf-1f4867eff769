/* BlockNote Editor Spacing Fixes */

/* Target BlockNote editor specifically */
.blocknote-editor-wrapper .bn-editor {
  /* Normalize spacing for all block elements */
  --bn-block-margin-top: 0.25em;
  --bn-block-margin-bottom: 0.25em;
}

/* Reduce paragraph spacing for normal single-enter behavior */
.blocknote-editor-wrapper .bn-editor [data-content-type='paragraph'] {
  min-height: 1.2em;
  margin-top: 0.25em !important;
  margin-bottom: 0.25em !important;
}

/* Ensure empty paragraphs are visible but don't take too much space */
.blocknote-editor-wrapper
  .bn-editor
  [data-content-type='paragraph']:empty::before {
  content: '\00a0'; /* Non-breaking space */
  color: transparent;
}

/* Fix heading spacing - keep this slightly larger */
.blocknote-editor-wrapper .bn-editor [data-content-type='heading'] {
  margin-top: 0.5em !important;
  margin-bottom: 0.3em !important;
}

/* Remove extra spacing from first block */
.blocknote-editor-wrapper .bn-editor > div:first-child [data-content-type] {
  margin-top: 0 !important;
}

/* Ensure list items have minimal spacing */
.blocknote-editor-wrapper .bn-editor [data-content-type='bulletListItem'],
.blocknote-editor-wrapper .bn-editor [data-content-type='numberedListItem'] {
  margin-top: 0.1em !important;
  margin-bottom: 0.1em !important;
}

/* Reduce line-height for tighter spacing */
.blocknote-editor-wrapper .bn-editor {
  line-height: 1.4;
}

/* Ensure consistent rendering between edit and view modes */
.blocknote-editor-wrapper .bn-editor .bn-block-content {
  min-height: 1.2em;
}

/* Fix spacing between blocks - make it minimal */
.blocknote-editor-wrapper .bn-editor .bn-block-outer {
  margin-bottom: 0 !important;
}

/* Reduce spacing for paragraphs with only whitespace */
.blocknote-editor-wrapper .bn-editor [data-content-type='paragraph'] p {
  min-height: 1.2em;
  margin: 0;
}

/* Ensure non-breaking spaces don't add extra height */
.blocknote-editor-wrapper
  .bn-editor
  [data-content-type='paragraph']
  p:empty::after {
  content: '\00a0';
  visibility: hidden;
  line-height: 1;
}

/* Special handling for first and last blocks */
.blocknote-editor-wrapper .bn-editor > div:last-child [data-content-type] {
  margin-bottom: 0 !important;
}

/* Override any default BlockNote spacing */
.blocknote-editor-wrapper .bn-editor .bn-block {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Fine-tune the actual content spacing */
.blocknote-editor-wrapper .bn-editor [data-content-type='paragraph'] > div {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

/* Ensure the first paragraph doesn't have top margin */
.blocknote-editor-wrapper
  .bn-editor
  > div:first-child
  [data-content-type='paragraph']
  > div {
  margin-top: 0;
}

/* Ensure the last paragraph doesn't have bottom margin */
.blocknote-editor-wrapper
  .bn-editor
  > div:last-child
  [data-content-type='paragraph']
  > div {
  margin-bottom: 0;
}
