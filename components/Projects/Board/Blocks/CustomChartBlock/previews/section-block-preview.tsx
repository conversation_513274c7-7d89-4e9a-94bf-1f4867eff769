'use client';

import {
  Activity,
  AlertCircle,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  Award,
  BarChart3,
  Battery,
  Bell,
  Bike,
  Bluetooth,
  Book,
  Cake,
  Calendar,
  Camera,
  Car,
  Check,
  CheckCircle,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Clock,
  Cloud,
  Coffee,
  CreditCard,
  DollarSign,
  Download,
  Edit,
  Eye,
  EyeOff,
  FileText,
  Filter,
  Flame,
  Folder,
  Gamepad2,
  Gift,
  Headphones,
  Heart,
  HelpCircle,
  Home,
  ImageIcon,
  Info,
  Laptop,
  Lock,
  Mail,
  MapPin,
  Menu,
  Minus,
  Monitor,
  Moon,
  MoreHorizontal,
  MoreVertical,
  Music,
  Phone,
  PieChart,
  Pizza,
  Plane,
  Plus,
  Search,
  Settings,
  Shield,
  ShoppingCart,
  Smartphone,
  Snowflake,
  Star,
  Sun,
  Target,
  Train,
  Trash2,
  TrendingDown,
  TrendingUp,
  Umbrella,
  Unlock,
  Upload,
  User,
  Utensils,
  Video,
  Wifi,
  WifiOff,
  Wine,
  X,
  XCircle,
  Zap,
} from 'lucide-react';
import dynamic from 'next/dynamic';
import type React from 'react';
import { LoadingFallback } from '../../../board.utils';
import { BCCNote } from './reusables';

// Utility function to get a darker complementary color
const getDarkerColor = (backgroundColor: string): string => {
  // Color mappings for better visual harmony
  const colorMappings: { [key: string]: string } = {
    '#ffffff': '#374151', // white -> gray-700
    '#f8fafc': '#374151', // slate-50 -> gray-700
    '#eff6ff': '#1d4ed8', // blue-50 -> blue-700
    '#f0fdf4': '#15803d', // green-50 -> green-700
    '#fefce8': '#a16207', // yellow-50 -> yellow-700
    '#faf5ff': '#7c3aed', // purple-50 -> purple-700
    '#fdf2f8': '#be185d', // pink-50 -> pink-700
    '#fff7ed': '#c2410c', // orange-50 -> orange-700
    '#fef2f2': '#dc2626', // red-50 -> red-700
    '#eef2ff': '#4338ca', // indigo-50 -> indigo-700
    '#f0fdfa': '#0f766e', // teal-50 -> teal-700
    '#ecfeff': '#0891b2', // cyan-50 -> cyan-700
  };

  // Return mapped color if available
  if (colorMappings[backgroundColor.toLowerCase()]) {
    return colorMappings[backgroundColor.toLowerCase()];
  }

  // Fallback: darken the color programmatically
  const hex = backgroundColor.replace('#', '');
  const r = Number.parseInt(hex.substr(0, 2), 16);
  const g = Number.parseInt(hex.substr(2, 2), 16);
  const b = Number.parseInt(hex.substr(4, 2), 16);

  // Darken significantly for better contrast
  const darkenedR = Math.max(0, Math.floor(r * 0.3));
  const darkenedG = Math.max(0, Math.floor(g * 0.3));
  const darkenedB = Math.max(0, Math.floor(b * 0.3));

  const toHex = (n: number) => n.toString(16).padStart(2, '0');
  return `#${toHex(darkenedR)}${toHex(darkenedG)}${toHex(darkenedB)}`;
};

// Import the actual board block components
const TextBoardBlock = dynamic(() => import('../../TextBoardBlock'), {
  loading: () => <LoadingFallback height="h-32" />,
});
const ChartBoardBlock = dynamic(() => import('../../ChartBoardBlock'), {
  loading: () => <LoadingFallback height="h-32" />,
});
const ImageBoardBlock = dynamic(() => import('../../ImageBoardBlock'), {
  loading: () => <LoadingFallback height="h-32" />,
});
const VideoBoardBlock = dynamic(() => import('../../VideoBoardBlock'), {
  loading: () => <LoadingFallback height="h-32" />,
});
const WhatsInsideBoardBlock = dynamic(
  () => import('../../WhatsInsideBoardBlock'),
  {
    loading: () => <LoadingFallback height="h-32" />,
  }
);
const CustomChartBoardBlock = dynamic(() => import('../CustomChartBlock'), {
  loading: () => <LoadingFallback height="h-32" />,
});
const TitleBoardBlock = dynamic(() => import('../../TitleBoardBlock'), {
  loading: () => <LoadingFallback height="h-32" />,
});

// Icon mapping for the title
const iconMap = {
  home: Home,
  user: User,
  settings: Settings,
  heart: Heart,
  star: Star,
  bell: Bell,
  mail: Mail,
  phone: Phone,
  calendar: Calendar,
  clock: Clock,
  'map-pin': MapPin,
  camera: Camera,
  image: ImageIcon,
  video: Video,
  music: Music,
  book: Book,
  'file-text': FileText,
  folder: Folder,
  download: Download,
  upload: Upload,
  search: Search,
  filter: Filter,
  edit: Edit,
  'trash-2': Trash2,
  plus: Plus,
  minus: Minus,
  check: Check,
  x: X,
  'arrow-right': ArrowRight,
  'arrow-left': ArrowLeft,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  'chevron-right': ChevronRight,
  'chevron-left': ChevronLeft,
  'chevron-up': ChevronUp,
  'chevron-down': ChevronDown,
  menu: Menu,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
  info: Info,
  'alert-circle': AlertCircle,
  'check-circle': CheckCircle,
  'x-circle': XCircle,
  'help-circle': HelpCircle,
  eye: Eye,
  'eye-off': EyeOff,
  lock: Lock,
  unlock: Unlock,
  shield: Shield,
  zap: Zap,
  flame: Flame,
  sun: Sun,
  moon: Moon,
  cloud: Cloud,
  umbrella: Umbrella,
  snowflake: Snowflake,
  target: Target,
  award: Award,
  gift: Gift,
  'shopping-cart': ShoppingCart,
  'credit-card': CreditCard,
  'dollar-sign': DollarSign,
  'trending-up': TrendingUp,
  'trending-down': TrendingDown,
  'bar-chart-3': BarChart3,
  'pie-chart': PieChart,
  activity: Activity,
  wifi: Wifi,
  'wifi-off': WifiOff,
  battery: Battery,
  bluetooth: Bluetooth,
  smartphone: Smartphone,
  laptop: Laptop,
  monitor: Monitor,
  headphones: Headphones,
  'gamepad-2': Gamepad2,
  car: Car,
  plane: Plane,
  train: Train,
  bike: Bike,
  coffee: Coffee,
  pizza: Pizza,
  utensils: Utensils,
  wine: Wine,
  cake: Cake,
};

interface SectionPreviewProps {
  block?: any;
  boardBlocks?: any[];
  from?: string;
  isScaled?: boolean;
  setReportBoards?: any;
  editingBlockId?: string | null;
  setEditingBlockId?: (id: string | null) => void;
  isAnyModalOpen?: boolean;
}

const ActualBlockPreview = ({
  block,
  isScaled = false,
  boardBlocks = [],
  setReportBoards,
  editingBlockId,
  setEditingBlockId,
  isAnyModalOpen,
  customHeight = 'auto', // Add this new prop
}: {
  block: any;
  isScaled?: boolean;
  boardBlocks?: any[];
  setReportBoards?: any;
  editingBlockId?: string | null;
  setEditingBlockId?: (id: string | null) => void;
  isAnyModalOpen?: boolean;
  customHeight?: string; // Add this new prop
}) => {
  // Common props for all block components
  const commonProps = {
    block,
    boardBlocks,
    setReportBoards: setReportBoards || (() => {}),
    editingBlockId,
    setEditingBlockId: setEditingBlockId || (() => {}),
    isAnyModalOpen: isAnyModalOpen || false,
  };

  // Enhanced height handling function
  const getHeightStyle = () => {
    const baseStyle: React.CSSProperties = {};

    if (customHeight && customHeight !== 'auto') {
      // Handle custom pixel values like "custom-180px"
      const customMatch = customHeight.match(/custom-(\d+)px/);
      if (customMatch) {
        const pixels = Number.parseInt(customMatch[1]);
        baseStyle.height = `${pixels}px`;
        baseStyle.minHeight = `${pixels}px`;
        return baseStyle;
      }

      // Handle standard Tailwind classes
      const heightMap: { [key: string]: string } = {
        'h-32': '128px',
        'h-40': '160px',
        'h-48': '192px',
        'h-56': '224px',
        'h-64': '256px',
      };

      if (heightMap[customHeight]) {
        baseStyle.height = heightMap[customHeight];
        baseStyle.minHeight = heightMap[customHeight];
        return baseStyle;
      }
    }

    // Default height for scaled/unscaled
    const defaultHeight = isScaled ? '200px' : '250px';
    baseStyle.minHeight = defaultHeight;
    return baseStyle;
  };

  const heightStyle = getHeightStyle();
  const containerClass = `    
    bg-white rounded-lg border border-gray-200 shadow-sm relative group    
    transition-all duration-200 hover:shadow-md    
    w-full flex flex-col
    ${customHeight && customHeight !== 'auto' ? 'overflow-hidden' : ''}  
  `;

  const renderBlockContent = () => {
    switch (block.type) {
      case 'title':
        return <TitleBoardBlock {...commonProps} />;
      case 'text':
        return <TextBoardBlock {...commonProps} />;
      case 'chart':
        return <ChartBoardBlock {...commonProps} />;
      case 'image':
        return <ImageBoardBlock {...commonProps} />;
      case 'video':
        return <VideoBoardBlock {...commonProps} />;
      case 'whats_inside':
        return <WhatsInsideBoardBlock {...commonProps} />;
      case 'custom_chart':
        return <CustomChartBoardBlock {...commonProps} />;
      default:
        return (
          <div className="p-6 flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <div className="text-2xl mb-2">📦</div>
              <div className="text-sm">Unknown Block Type</div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={containerClass} style={heightStyle}>
      {renderBlockContent()}
    </div>
  );
};

// Enhanced grid calculation function with support for advanced row configuration
const calculateGridLayout = (
  blockCount: number,
  viewMode: 'grid' | 'stacked',
  gridConfig?: any
) => {
  if (viewMode === 'stacked') {
    return 'flex flex-col gap-6';
  }

  const {
    gridMode = 'auto',
    customRows = 2,
    customCols = 3,
    rowConfiguration = [],
  } = gridConfig || {};

  // Advanced mode: Custom row configuration
  if (
    gridMode === 'advanced' &&
    rowConfiguration &&
    rowConfiguration.length > 0
  ) {
    return 'flex flex-col gap-6'; // We'll handle row-by-row layout in distributeGridItems
  }

  // Manual mode: Uniform grid
  if (gridMode === 'manual' && blockCount > 1) {
    const cols = Math.min(customCols, 6); // Limit to 6 columns max for responsive design
    if (cols === 1) return 'grid grid-cols-1 gap-6';
    if (cols === 2) return 'grid grid-cols-1 sm:grid-cols-2 gap-6';
    if (cols === 3)
      return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';
    if (cols === 4)
      return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6';
    if (cols === 5)
      return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6';
    if (cols >= 6)
      return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-6';
  }

  // Auto mode: Smart grid configuration
  if (blockCount === 1) {
    return 'grid grid-cols-1 gap-6';
  } else if (blockCount === 2) {
    return 'grid grid-cols-1 sm:grid-cols-2 gap-6';
  } else if (blockCount === 3) {
    return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6';
  } else if (blockCount === 4) {
    return 'grid grid-cols-1 sm:grid-cols-2 gap-6'; // 2x2 grid
  } else if (blockCount === 5) {
    return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'; // First row: 3, Second row: 2
  } else if (blockCount === 6) {
    return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'; // 3x2 grid
  } else if (blockCount > 6) {
    return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6'; // Default to 3 columns for larger counts
  }

  return 'grid grid-cols-1 gap-6';
};

// Enhanced grid item distribution with advanced row configuration support
const distributeGridItems = (
  blocks: any[],
  viewMode: 'grid' | 'stacked',
  gridConfig?: any
) => {
  if (viewMode === 'stacked') {
    return {
      type: 'stacked',
      items: blocks.map((block, index) => ({
        block,
        span: 1,
        key: `${block._id}-${index}`,
      })),
    };
  }

  const blockCount = blocks.length;
  const {
    gridMode = 'auto',
    customRows = 2,
    rowConfiguration = [],
  } = gridConfig || {};

  // Advanced mode: Custom row configuration
  if (
    gridMode === 'advanced' &&
    rowConfiguration &&
    rowConfiguration.length > 0
  ) {
    const rows: any[][] = [];
    let blockIndex = 0;

    // Distribute blocks according to row configuration
    for (const blocksInRow of rowConfiguration) {
      const rowBlocks = blocks.slice(blockIndex, blockIndex + blocksInRow);
      if (rowBlocks.length > 0) {
        rows.push(rowBlocks);
        blockIndex += blocksInRow;
      }
    }

    return {
      type: 'advanced',
      rows: rows.map((rowBlocks, rowIndex) => ({
        blocks: rowBlocks,
        key: `row-${rowIndex}`,
        blocksCount: rowBlocks.length,
      })),
    };
  }

  // Manual and Auto modes: Standard grid
  if (gridMode === 'manual' && blockCount > 1) {
    // Manual distribution - evenly distribute across specified rows
    return {
      type: 'uniform',
      items: blocks.map((block, index) => ({
        block,
        span: 1,
        key: `${block._id}-${index}`,
      })),
    };
  }

  // Auto distribution with smart spanning
  if (blockCount === 5) {
    // Special case for 5 blocks: first row gets 3, second row gets 2 (but spans to fill width)
    return {
      type: 'uniform',
      items: blocks.map((block, index) => ({
        block,
        span: index >= 3 ? 'col-span-3 sm:col-span-1' : 1, // Last 2 blocks span more on mobile
        key: `${block._id}-${index}`,
      })),
    };
  }

  // Default: no special spanning
  return {
    type: 'uniform',
    items: blocks.map((block, index) => ({
      block,
      span: 1,
      key: `${block._id}-${index}`,
    })),
  };
};

// Helper function to get responsive grid classes for a given number of columns
const getResponsiveGridClass = (cols: number) => {
  if (cols === 1) return 'grid grid-cols-1 gap-4';
  if (cols === 2) return 'grid grid-cols-1 sm:grid-cols-2 gap-4';
  if (cols === 3) return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4';
  if (cols === 4) return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4';
  if (cols === 5) return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4';
  if (cols >= 6) return 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-6 gap-4';
  return 'grid grid-cols-1 gap-4';
};

export default function SectionBlockPreview({
  block,
  boardBlocks = [],
  from,
  isScaled = false,
  setReportBoards,
  editingBlockId,
  setEditingBlockId,
  isAnyModalOpen,
}: SectionPreviewProps) {
  const { customChartConfig } = block || {};
  const {
    title = 'Section Title',
    description = '',
    backgroundColor = '#ffffff',
    borderColor = '#e5e7eb',
    notes = '',
    notesBg = '#ffffff',
    viewMode: savedViewMode = 'grid',
    titleIcon = 'none',
    gridConfig,
    blockHeights = {}, // Add this to get block heights
  } = customChartConfig || {};

  // Use saved view mode from configuration
  const currentViewMode = customChartConfig?.viewMode || 'grid';

  // FIXED: Always use the selectedBlocks order for both form and main preview
  const selectedBlockObjects = (() => {
    const selectedBlockIds = customChartConfig?.selectedBlocks || [];

    if (from === 'form') {
      // For form preview, use the selectedBlocks array from customChartConfig
      return selectedBlockIds
        .map((blockId) => boardBlocks.find((b) => b._id === blockId))
        .filter(Boolean);
    } else {
      // FIXED: For main board view, also respect the selectedBlocks order
      // First, get all blocks that belong to this section
      const sectionBlocks = boardBlocks.filter(
        (b) => b.parentSection === block._id
      );

      // If we have selectedBlocks defined, use that order
      // if (selectedBlockIds.length > 0) {
      //   return selectedBlockIds
      //     .map((blockId) => sectionBlocks.find((b) => b._id === blockId))
      //     .filter(Boolean);
      // }
      if (selectedBlockIds.length > 0) {
        const bls = selectedBlockIds
          .map((blockId) => sectionBlocks.find((b) => b._id === blockId))
          .filter(Boolean);
        if (!bls?.length || bls?.length === 0) {
          return sectionBlocks;
        } else {
          return bls;
        }
      }

      // Fallback: return section blocks in their original order
      return sectionBlocks;
    }
  })();

  // Determine if this is form preview or board view
  const isFormPreview = from === 'form';

  // Enhanced grid configuration
  const gridClass = calculateGridLayout(
    selectedBlockObjects.length,
    currentViewMode,
    gridConfig
  );
  const distributedBlocks: any = distributeGridItems(
    selectedBlockObjects,
    currentViewMode,
    gridConfig
  );

  const containerPadding = isFormPreview ? 'p-6' : 'p-8';
  const maxWidth = isFormPreview ? 'max-w-5xl' : 'w-full';

  // Get the icon component
  const getIconComponent = () => {
    const iconValue = titleIcon && titleIcon !== 'none' ? titleIcon : null;
    if (!iconValue) return null;
    const IconComponent = iconMap[iconValue as keyof typeof iconMap];
    return IconComponent || null;
  };

  const IconComponent = getIconComponent();
  const iconColor = getDarkerColor(backgroundColor);

  // Render blocks based on distribution type
  const renderBlocks = () => {
    if (selectedBlockObjects.length === 0) {
      return (
        <div className="text-center py-16 text-gray-500">
          <div className="w-20 h-20 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <span className="text-3xl">📁</span>
          </div>
          <div className="text-lg font-medium mb-2">No blocks selected</div>
          <div className="text-sm">
            {from === 'form'
              ? 'Select blocks from the left panel to preview them here'
              : 'This section is empty'}
          </div>
        </div>
      );
    }

    // Advanced mode: Row-by-row layout
    if (distributedBlocks.type === 'advanced') {
      return (
        <div className="space-y-6">
          {distributedBlocks.rows.map(
            ({ blocks: rowBlocks, key, blocksCount }) => (
              <div key={key} className={getResponsiveGridClass(blocksCount)}>
                {rowBlocks.map((rowBlock, blockIndex) => (
                  <div key={`${rowBlock._id}-${blockIndex}`} className="w-full">
                    <ActualBlockPreview
                      block={rowBlock}
                      isScaled={isFormPreview}
                      boardBlocks={boardBlocks}
                      setReportBoards={setReportBoards}
                      editingBlockId={editingBlockId}
                      setEditingBlockId={setEditingBlockId}
                      isAnyModalOpen={isAnyModalOpen}
                      customHeight={blockHeights[rowBlock._id] || 'auto'}
                    />
                  </div>
                ))}
              </div>
            )
          )}
        </div>
      );
    }

    // Stacked mode
    if (distributedBlocks.type === 'stacked') {
      return (
        <div className="space-y-6">
          {distributedBlocks.items.map(({ block: selectedBlock, key }) => (
            <div key={key} className="w-full">
              <ActualBlockPreview
                block={selectedBlock}
                isScaled={isFormPreview}
                boardBlocks={boardBlocks}
                setReportBoards={setReportBoards}
                editingBlockId={editingBlockId}
                setEditingBlockId={setEditingBlockId}
                isAnyModalOpen={isAnyModalOpen}
                customHeight={blockHeights[selectedBlock._id] || 'auto'}
              />
            </div>
          ))}
        </div>
      );
    }

    // Uniform grid mode (manual and auto)
    return (
      <div className={`${gridClass} items-start`}>
        {distributedBlocks.items.map(({ block: selectedBlock, span, key }) => (
          <div
            key={key}
            className={`w-full ${typeof span === 'string' ? span : ''}`}
          >
            <ActualBlockPreview
              block={selectedBlock}
              isScaled={isFormPreview}
              boardBlocks={boardBlocks}
              setReportBoards={setReportBoards}
              editingBlockId={editingBlockId}
              setEditingBlockId={setEditingBlockId}
              isAnyModalOpen={isAnyModalOpen}
              customHeight={blockHeights[selectedBlock._id] || 'auto'}
            />
          </div>
        ))}
      </div>
    );
  };

  return (
    <div
      className={`${
        isFormPreview ? 'bg-white rounded-lg' : 'w-full h-full'
      } ${maxWidth}`}
    >
      <div
        className={`w-full h-full rounded-xl ${containerPadding}`}
        style={{
          backgroundColor,
          borderColor,
          minHeight: isFormPreview ? '500px' : 'auto',
        }}
      >
        {/* Section Header with Icon */}
        <div className="mb-8">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              {IconComponent && (
                <IconComponent
                  className="w-6 h-6"
                  style={{ color: iconColor }}
                />
              )}
              <h3
                className={`font-bold text-gray-900 ${
                  isFormPreview ? 'text-xl' : 'text-2xl'
                }`}
              >
                {title}
              </h3>
            </div>
            {description && (
              <p
                className={`text-gray-600 ${
                  isFormPreview ? 'text-sm' : 'text-base'
                } ${IconComponent ? 'ml-9' : ''}`}
              >
                {description}
              </p>
            )}
          </div>
        </div>

        {/* Selected Blocks Grid/Stack */}
        <div className="space-y-3">{renderBlocks()}</div>

        {notes && notes.trim() && <BCCNote bg={notesBg} note={notes} />}
      </div>

      {/* Empty State for Form Preview */}
      {isFormPreview && !title && selectedBlockObjects.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="w-24 h-24 rounded-xl bg-gray-100 flex items-center justify-center mx-auto mb-6">
              <span className="text-4xl">📋</span>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Configure Your Section
            </h3>
            <p className="text-gray-500 text-sm max-w-sm">
              Add a title, description, and select blocks to see a beautiful
              preview of your section
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
