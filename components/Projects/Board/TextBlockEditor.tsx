/* eslint-disable @typescript-eslint/ban-ts-comment */
'use client';

import api from '@/lib/api';
import useUserStore from '@/zustand/useUserStore';
import {
  BlockNoteSchema,
  combineByGroup,
  filterSuggestionItems,
} from '@blocknote/core';
import '@blocknote/core/fonts/inter.css';
import { en } from '@blocknote/core/locales';
import { BlockNoteView } from '@blocknote/mantine';
import '@blocknote/mantine/style.css';
import {
  getDefaultReactSlashMenuItems,
  SuggestionMenuController,
  useCreateBlockNote,
} from '@blocknote/react';
import {
  getMultiColumnSlashMenuItems,
  multiColumnDropCursor,
  locales as multiColumnLocales,
  withMultiColumn,
} from '@blocknote/xl-multi-column';
import axios from 'axios';
import { useEffect, useMemo } from 'react';

const debounce = (func: any, delay: any) => {
  let timeoutId: any;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Helper function to preserve empty paragraphs in HTML
const preserveEmptyParagraphs = (html: string): string => {
  // Replace empty paragraphs with paragraphs containing a non-breaking space
  return html
    .replace(/<p><\/p>/g, '<p>&nbsp;</p>')
    .replace(/<p>\s*<\/p>/g, '<p>&nbsp;</p>')
    .replace(/<p><br><\/p>/g, '<p>&nbsp;</p>');
};

// Helper function to restore empty paragraphs when loading
const restoreEmptyParagraphs = (html: string): string => {
  // Keep the non-breaking spaces as they help maintain spacing
  return html;
};

export default function TextBlockEditor({
  block,
  htmlContent,
  setHtmlContent,
  initialContent,
  setInitialContent,
  viewOnly,
  isInsight,
  editMode = true,
  aiGeneratedContent,
  setAiGeneratedContent,
}: any) {
  const { user } = useUserStore();

  const handleFileUpload = async (file: any) => {
    try {
      if (!file) throw new Error('No file selected');
      const formData = new FormData();
      formData.append('file', file);
      formData.append('userId', user?._id);

      const { data: presignedResponse } = await api.post(
        `/media/presigned/upload/small`,
        { fileName: file.name, fileType: file.type },
        { headers: { 'Content-Type': 'application/json' } }
      );

      const { presignedUrl, key } = presignedResponse.data;

      await axios.put(presignedUrl, file, {
        headers: {
          'Content-Type': file.type,
        },
      });

      const fileUrl = key && `${process.env.NEXT_PUBLIC_S3_LINK}${key}`;
      return fileUrl;
    } catch (error) {
      return null;
    }
  };

  const editor = useCreateBlockNote({
    uploadFile: handleFileUpload,
    // Adds column and column list blocks to the schema.
    schema: withMultiColumn(BlockNoteSchema.create()),
    // The default drop cursor only shows up above and below blocks - we replace
    // it with the multi-column one that also shows up on the sides of blocks.
    dropCursor: multiColumnDropCursor,
    // Merges the default dictionary with the multi-column dictionary.
    dictionary: {
      ...en,
      multi_column: multiColumnLocales.en,
      placeholders: {
        ...en.placeholders,
        default: 'start writing or press "/" for commands...',
        heading: 'start writing...',
      },
    },
  });

  // Gets the default slash menu items merged with the multi-column ones.
  const getSlashMenuItems = useMemo(() => {
    return async (query) =>
      filterSuggestionItems(
        combineByGroup(
          getCustomSlashMenuItems(editor),
          getMultiColumnSlashMenuItems(editor)
        ),
        query
      );
  }, [editor]);

  const loadInitialContent = async (data: any) => {
    if (editor && data) {
      const content = data.type === 'title' ? data?.title : data.content;
      // Restore empty paragraphs when loading content
      const processedContent = restoreEmptyParagraphs(content ?? '');

      try {
        const blocks = await editor?.tryParseHTMLToBlocks(processedContent);
        editor.replaceBlocks(editor.document, blocks);
        setInitialContent(content ?? '');
      } catch (error) {
        console.error('Error parsing HTML content:', error);
        // Fallback: create a single paragraph block
        const fallbackBlocks = await editor?.tryParseHTMLToBlocks('<p></p>');
        editor.replaceBlocks(editor.document, fallbackBlocks);
        setInitialContent('');
      }
    }
  };

  // Handle AI generated content
  const loadAIGeneratedContent = async (htmlContent: string) => {
    if (editor && htmlContent) {
      try {
        const processedContent = restoreEmptyParagraphs(htmlContent);
        const blocks = await editor.tryParseHTMLToBlocks(processedContent);
        editor.replaceBlocks(editor.document, blocks);
        // Clear the AI generated content flag after loading
        setAiGeneratedContent(null);
      } catch (error) {
        console.error('Error parsing AI generated HTML:', error);
        // Fallback: try to insert as plain text
        const blocks = await editor.tryParseHTMLToBlocks(
          `<p>${htmlContent}</p>`
        );
        editor.replaceBlocks(editor.document, blocks);
        setAiGeneratedContent(null);
      }
    }
  };

  const debouncedHandleEditorChange = debounce(async () => {
    if (editor) {
      try {
        const html = await editor.blocksToHTMLLossy(editor.document);
        // Preserve empty paragraphs before setting content
        const preservedHtml = preserveEmptyParagraphs(html);
        setHtmlContent(preservedHtml);
      } catch (error) {
        console.error('Error converting blocks to HTML:', error);
      }
    }
  }, 50);

  useEffect(() => {
    if (block) {
      loadInitialContent(block);
    }
  }, [block]);

  // Handle AI generated content updates
  useEffect(() => {
    if (aiGeneratedContent) {
      loadAIGeneratedContent(aiGeneratedContent);
    }
  }, [aiGeneratedContent, editor]);

  const getCustomSlashMenuItems = (editor) => {
    const defaultSlashItems = getDefaultReactSlashMenuItems(editor);
    // Filter out media-related items by their key or title
    const filteredItems = defaultSlashItems.filter((item: any) => {
      const excludeKeys = ['emoji'];
      return (
        !excludeKeys.includes(item.key?.toLowerCase()) &&
        !excludeKeys.includes(item.title?.toLowerCase())
      );
    });
    return filteredItems;
  };

  return (
    <>
      <div className="py-6">
        <div className="blocknote-editor-wrapper">
          <BlockNoteView
            slashMenu={false}
            editor={editor}
            onChange={
              !viewOnly && !isInsight ? debouncedHandleEditorChange : undefined
            }
            theme="light"
            editable={!viewOnly && !isInsight && editMode}
          >
            <SuggestionMenuController
              triggerCharacter={'/'}
              getItems={getSlashMenuItems}
            />
          </BlockNoteView>
        </div>
      </div>
    </>
  );
}
