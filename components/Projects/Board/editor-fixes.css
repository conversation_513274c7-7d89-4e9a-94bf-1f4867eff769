/* BlockNote Editor Spacing Fixes */

/* Target BlockNote editor specifically */
.blocknote-editor-wrapper .bn-editor {
  /* Normalize spacing for all block elements */
  --bn-block-margin-top: 0.5em;
  --bn-block-margin-bottom: 0.5em;
}

/* Fix heading spacing specifically */
.blocknote-editor-wrapper .bn-editor [data-content-type='heading'] {
  margin-top: 0.75em !important;
  margin-bottom: 0.5em !important;
}

/* Ensure consistent paragraph spacing */
.blocknote-editor-wrapper .bn-editor [data-content-type='paragraph'] {
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
}

/* Remove extra spacing from first block */
.blocknote-editor-wrapper .bn-editor > div:first-child [data-content-type] {
  margin-top: 0 !important;
}

/* Ensure list items have consistent spacing */
.blocknote-editor-wrapper .bn-editor [data-content-type='bulletListItem'],
.blocknote-editor-wrapper .bn-editor [data-content-type='numberedListItem'] {
  margin-top: 0.25em !important;
  margin-bottom: 0.25em !important;
}

/* Fix any potential line-height issues */
.blocknote-editor-wrapper .bn-editor {
  line-height: 1.6;
}

/* Ensure consistent rendering between edit and view modes */
.blocknote-editor-wrapper .bn-editor .bn-block-content {
  min-height: auto;
}

/* Fix spacing between blocks */
.blocknote-editor-wrapper .bn-editor .bn-block-outer {
  margin-bottom: 0 !important;
}

/* Normalize spacing for all block types */
.blocknote-editor-wrapper .bn-editor [data-content-type] {
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
}

/* Special handling for first and last blocks */
.blocknote-editor-wrapper .bn-editor > div:first-child [data-content-type] {
  margin-top: 0 !important;
}

.blocknote-editor-wrapper .bn-editor > div:last-child [data-content-type] {
  margin-bottom: 0 !important;
}
