/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/ban-types */
'use client';

import dynamic from 'next/dynamic';
import { BoardEmptyState, LoadingFallback } from './board.utils';

const AddMediaBlockSheet = dynamic(
  () => import('./Blocks/AddMediaBlockSheet'),
  {
    loading: () => <LoadingFallback height="h-full" />,
  }
);

const CreateCustomChartBlockModal = dynamic(
  () => import('./Blocks/CustomChartBlock/CreateCustomChartBlockModal'),
  {
    loading: () => <LoadingFallback height="h-full" />,
  }
);

import useRouteType from '@/hooks/useRouteType';
import api from '@/lib/api';
import { errorToast, successToast } from '@/utils/toast';
import useReportStore from '@/zustand/useReportStore';
import { useMutation } from '@tanstack/react-query';
import { Chart as ChartJS, registerables } from 'chart.js';
import {
  default as ChartDataLabels,
  default as ChartjsPluginWatermark,
} from 'chartjs-plugin-datalabels';
import {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import type { CustomChartType } from './Blocks/CustomChartBlock/cc-block.type';
import { CustomSidebar } from './board-sidebar';
import { globalStyles } from './board.styles';
import type { TBoardBlock } from './board.types';
import BoardSkeleton from './BoardSkeleton';
import BrandPerformanceHeader from './BrandPerformanceHeader';
import { DropZone } from './drop-zone';

const SingleBoardBlock = dynamic(() => import('./SingleBoardBlock'), {
  loading: () => <LoadingFallback height="h-full" />,
});

const AllBoardTemplates = dynamic(
  () => import('./Template/AllBoardTemplates'),
  {
    loading: () => <LoadingFallback height="h-full" />,
  }
);

const ResponsiveGridLayout = WidthProvider(Responsive);

// Create a context for tracking editing state
const EditingContext = createContext<{
  editingBlockId: string | null;
  setEditingBlockId: (id: string | null) => void;
}>({
  editingBlockId: null,
  setEditingBlockId: () => {},
});

// Loading Overlay Component
const LoadingOverlay = ({
  isVisible,
  message,
}: {
  isVisible: boolean;
  message: string;
}) => {
  if (!isVisible) return null;

  return (
    <div
      className="fixed inset-0 z-[9999] bg-black bg-opacity-20 backdrop-blur-sm"
      style={{
        pointerEvents: 'all',
        userSelect: 'none',
      }}
    >
      {/* Message at top right */}
      <div className="absolute top-4 right-4 bg-white shadow-lg rounded-lg px-4 py-3 border border-gray-200 flex items-center space-x-3 animate-pulse">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
          <div
            className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
            style={{ animationDelay: '0.1s' }}
          ></div>
          <div
            className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
            style={{ animationDelay: '0.2s' }}
          ></div>
        </div>
        <span className="text-sm font-medium text-gray-700">{message}</span>
      </div>
    </div>
  );
};

interface IBoardProps {
  boardBlocks: TBoardBlock[];
  setReportBoards: any;
  reportId: string;
  tabId: string;
  loading?: boolean;
  currentBoard: {
    title: string;
    _id: string;
    hasTemplates: boolean;
  };
  setCurrentBoard: any;
}

export default function Board({
  boardBlocks,
  setReportBoards,
  reportId,
  tabId,
  loading,
  currentBoard,
  setCurrentBoard,
}: IBoardProps) {
  ChartJS.register(...registerables, ChartDataLabels, ChartjsPluginWatermark);

  // Simplified state management
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isTemplateOpen, setIsTemplateOpen] = useState(false);
  const [isAddCustomCharBlockModalOpen, setIsAddCustomCharBlockModalOpen] =
    useState(false);
  const [isAddMediaBlockSheetOpen, setIsAddMediaBlockSheetOpen] =
    useState(false);
  const [selectedBlockType, setSelectedBlockType] = useState('text');
  const [selectedCustomChartType, setSelectedCustomChartType] = useState<
    CustomChartType | undefined
  >(undefined);
  const [isDragging, setIsDragging] = useState(false);
  const [editingBlockId, setEditingBlockId] = useState<string | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [showEmptyState, setShowEmptyState] = useState(false);
  const [isComponentReady, setIsComponentReady] = useState(false);

  // NEW: Loading overlay state
  const [showLoadingOverlay, setShowLoadingOverlay] = useState(false);
  const loadingOverlayTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { isInsight } = useRouteType();
  const { viewOnly } = useReportStore();

  // Drop zone state
  const [dropZones, setDropZones] = useState<Array<{ x: number; y: number }>>(
    []
  );
  const [pendingDropPosition, setPendingDropPosition] = useState<{
    x: number;
    y: number;
  } | null>(null);

  // FIXED: Stable layout management with per-tab caching
  const [layoutCache, setLayoutCache] = useState<Record<string, any>>({});
  const [currentLayouts, setCurrentLayouts] = useState<any>({
    lg: [],
    md: [],
    sm: [],
    xs: [],
    xxs: [],
  });

  // CRITICAL: Refs to prevent race conditions
  const currentTabRef = useRef<string>(tabId);
  const isEditingRef = useRef(false);
  const gridInstanceRef = useRef<any>(null);
  const contentAreaRef = useRef<HTMLDivElement>(null);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isProcessingDropRef = useRef(false);
  const layoutUpdateTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTabSwitchingRef = useRef(false);

  // NEW: Function to show loading overlay with customizable duration
  const showLoadingOverlayWithTimeout = useCallback(
    (message = 'Please wait a moment to get layout ready', duration = 3000) => {
      // Clear any existing timeout
      if (loadingOverlayTimeoutRef.current) {
        clearTimeout(loadingOverlayTimeoutRef.current);
      }

      setShowLoadingOverlay(true);

      // Hide overlay after specified duration
      loadingOverlayTimeoutRef.current = setTimeout(() => {
        setShowLoadingOverlay(false);
      }, duration);
    },
    []
  );

  // NEW: Helper function to check if this is the first content being added
  const topLevelBlocks = useMemo(() => {
    if (!boardBlocks) return [];
    return boardBlocks?.filter((block) => !block.parentSection);
  }, [boardBlocks]);

  const isFirstContentAddition = useCallback(() => {
    return !topLevelBlocks || topLevelBlocks.length === 0;
  }, [topLevelBlocks]);

  // NEW: Detect page reload/refresh
  useEffect(() => {
    const handleBeforeUnload = () => {
      // This will be triggered on page reload
      showLoadingOverlayWithTimeout('Please wait a moment to get layout ready');
    };

    const handleVisibilityChange = () => {
      // This can help detect when user comes back to the tab
      if (!document.hidden) {
        showLoadingOverlayWithTimeout(
          'Please wait a moment to get layout ready'
        );
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Show overlay on initial load
    if (!isComponentReady) {
      showLoadingOverlayWithTimeout('Please wait a moment to get layout ready');
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [showLoadingOverlayWithTimeout, isComponentReady]);

  // Update current tab ref when tabId changes
  useEffect(() => {
    if (currentTabRef.current !== tabId) {
      console.log(`Tab switching from ${currentTabRef.current} to ${tabId}`);
      isTabSwitchingRef.current = true;
      currentTabRef.current = tabId;

      // NEW: Show loading overlay on tab change
      showLoadingOverlayWithTimeout('Please wait a moment to get layout ready');

      // Clear any pending layout updates from previous tab
      if (layoutUpdateTimeoutRef.current) {
        clearTimeout(layoutUpdateTimeoutRef.current);
        layoutUpdateTimeoutRef.current = null;
      }

      // Reset tab switching flag after a short delay
      setTimeout(() => {
        isTabSwitchingRef.current = false;
      }, 100);
    }
  }, [tabId, showLoadingOverlayWithTimeout]);

  // Handle initial load
  useEffect(() => {
    if (!loading && boardBlocks !== undefined) {
      setTimeout(() => setIsComponentReady(true), 100);
    }
  }, [loading, boardBlocks]);

  // Empty state logic - simplified
  useEffect(() => {
    if (loading || (topLevelBlocks && topLevelBlocks.length > 0)) {
      setShowEmptyState(false);
      return;
    }

    if (
      !loading &&
      topLevelBlocks !== undefined &&
      topLevelBlocks.length === 0
    ) {
      const timer = setTimeout(() => {
        if (!loading && topLevelBlocks && topLevelBlocks.length === 0) {
          setShowEmptyState(true);
        }
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [loading, topLevelBlocks]);

  // Add global styles
  useEffect(() => {
    const styleEl = document.createElement('style');
    styleEl.textContent =
      globalStyles +
      `
      .layout.editing-locked {
        pointer-events: none !important;
      }
      .layout.editing-locked * {
        transition: none !important;
        transform: none !important;
      }
      .layout.editing-locked .react-grid-item.editing-active {
        pointer-events: auto !important;
        z-index: 1000 !important;
      }
      .editing-locked .react-resizable-handle {
        display: none !important;
      }
      .react-grid-item[data-block-type="section"] {
        min-height: 240px !important;
      }
    `;
    document.head.appendChild(styleEl);
    return () => {
      if (document.head.contains(styleEl)) {
        document.head.removeChild(styleEl);
      }
    };
  }, []);

  // Enhanced editing state management
  const setEditingBlockIdSafe = useCallback((id: string | null) => {
    const wasEditing = isEditingRef.current;
    const isNowEditing = id !== null;
    isEditingRef.current = isNowEditing;
    setEditingBlockId(id);

    // Freeze/unfreeze grid
    if (!wasEditing && isNowEditing && gridInstanceRef.current) {
      const gridElement = gridInstanceRef.current.containerRef?.current;
      if (gridElement) {
        gridElement.classList.add('editing-locked');
      }
    }

    if (wasEditing && !isNowEditing && gridInstanceRef.current) {
      const gridElement = gridInstanceRef.current.containerRef?.current;
      if (gridElement) {
        setTimeout(() => {
          gridElement.classList.remove('editing-locked');
        }, 100);
      }
    }
  }, []);

  // Calculate dynamic height based on content - PRESERVE EXISTING HEIGHTS
  const calculateContentHeight = useCallback(
    (block: TBoardBlock, preserveExisting = true) => {
      const baseHeight = 4;

      // CRITICAL: Always preserve existing height if it exists and preserveExisting is true
      if (preserveExisting && block.layout?.h && block.layout.h > 0) {
        return block.layout.h;
      }

      switch (block.type) {
        case 'title': {
          const title = block.title || 'This is your title, edit here!';
          const estimatedLines = Math.ceil(title.length / 50);
          return Math.max(baseHeight, Math.ceil(estimatedLines * 0.4) + 1);
        }
        case 'text': {
          const content = block.content || 'Edit your content here!';
          const estimatedLines = Math.ceil(content.length / 80);
          return Math.max(baseHeight + 1, Math.ceil(estimatedLines * 0.3) + 2);
        }
        case 'chart':
        case 'custom_chart': {
          // @ts-ignore
          if (block?.customChartType === 'overview_block') {
            return Math.max(2, baseHeight);
            // @ts-ignore
          } else if (block?.customChartType === 'section') {
            // For section blocks, ALWAYS preserve existing height if available
            if (block.layout?.h && block.layout.h > 0) {
              return block.layout.h;
            }
            // @ts-ignore
            const selectedBlocks =
              block?.customChartConfig?.selectedBlocks || [];
            const rows = Math.ceil(selectedBlocks.length / 3);
            const blocksHeight = rows * 4;
            const headerHeight = 3;
            const padding = 2;
            return Math.max(6, headerHeight + blocksHeight + padding);
          } else {
            return Math.max(6, baseHeight + 6);
          }
        }
        case 'image':
        case 'video': {
          return Math.max(5, baseHeight + 3);
        }
        default:
          return baseHeight + 2;
      }
    },
    []
  );

  // FIXED: Generate layout from blocks - ALWAYS USE BACKEND LAYOUT DATA FIRST
  const generateLayoutForBlocks = useCallback(
    (blocks: any[]) => {
      return blocks.map((block, index) => {
        // CRITICAL: ALWAYS use backend layout data if it exists
        if (block.layout && typeof block.layout === 'object') {
          return {
            i: block._id,
            x: block.layout.x ?? 0,
            y: block.layout.y ?? 0,
            w: block.layout.w ?? 6,
            h: block.layout.h ?? 4,
            minW: 3,
            minH:
              block.type === 'custom_chart' &&
              block.customChartType === 'section'
                ? 6
                : 2,
            maxW: 12,
            maxH: 50,
          };
        }

        // Only calculate if no backend layout exists
        const height = calculateContentHeight(block, false);
        return {
          i: block._id,
          x: (index % 2) * 6,
          y: Math.floor(index / 2) * height,
          w: 6,
          h: height,
          minW: 3,
          minH:
            block.type === 'custom_chart' && block.customChartType === 'section'
              ? 6
              : 2,
          maxW: 12,
          maxH: 50,
        };
      });
    },
    [calculateContentHeight]
  );

  // FIXED: Stable layout management with proper caching
  const updateLayoutsForCurrentTab = useCallback(
    (blocks: any[]) => {
      if (!blocks || blocks.length === 0 || isTabSwitchingRef.current) {
        return;
      }

      const currentTab = currentTabRef.current;
      console.log(`Updating layouts for tab: ${currentTab}`);

      // Check if we have cached layouts for this tab
      const cachedLayouts = layoutCache[currentTab];

      // If we have cached layouts and the block count matches, use them
      if (
        cachedLayouts &&
        cachedLayouts.lg &&
        cachedLayouts.lg.length === blocks.length
      ) {
        const allBlocksMatch = blocks.every((block) =>
          cachedLayouts.lg.some((layout: any) => layout.i === block._id)
        );

        if (allBlocksMatch) {
          console.log(`Using cached layouts for tab: ${currentTab}`);
          setCurrentLayouts(cachedLayouts);
          return;
        }
      }

      // Generate new layouts from blocks
      const defaultLayout = generateLayoutForBlocks(blocks);
      const newLayouts = {
        lg: defaultLayout,
        md: defaultLayout.map((item) => ({ ...item, w: Math.min(item.w, 6) })),
        sm: defaultLayout.map((item) => ({ ...item, w: Math.min(item.w, 6) })),
        xs: defaultLayout.map((item) => ({ ...item, w: 4, x: 0 })),
        xxs: defaultLayout.map((item) => ({ ...item, w: 2, x: 0 })),
      };

      console.log(
        `Generated new layouts for tab: ${currentTab}`,
        newLayouts.lg.map((l) => ({ id: l.i, h: l.h }))
      );

      // Update current layouts
      setCurrentLayouts(newLayouts);

      // Cache the layouts for this tab
      setLayoutCache((prev) => ({
        ...prev,
        [currentTab]: newLayouts,
      }));
    },
    [generateLayoutForBlocks, layoutCache]
  );

  // FIXED: Debounced layout updates to prevent race conditions
  useEffect(() => {
    if (
      topLevelBlocks &&
      topLevelBlocks.length > 0 &&
      isComponentReady &&
      !isEditingRef.current
    ) {
      // Clear any existing timeout
      if (layoutUpdateTimeoutRef.current) {
        clearTimeout(layoutUpdateTimeoutRef.current);
      }

      // Debounce layout updates to prevent race conditions during rapid tab switching
      layoutUpdateTimeoutRef.current = setTimeout(() => {
        // Only update if we're still on the same tab
        if (currentTabRef.current === tabId && !isTabSwitchingRef.current) {
          updateLayoutsForCurrentTab(topLevelBlocks);
        }
      }, 150); // Small delay to prevent race conditions
    }

    return () => {
      if (layoutUpdateTimeoutRef.current) {
        clearTimeout(layoutUpdateTimeoutRef.current);
      }
    };
  }, [topLevelBlocks, isComponentReady, tabId, updateLayoutsForCurrentTab]);

  // Save layout changes mutation
  const saveLayoutMutation = useMutation({
    mutationFn: async (layoutUpdates: any[]) => {
      console.log('Saving layout updates to backend:', layoutUpdates);
      const response = await api.patch(
        `/board/board-blocks/layout/${reportId}/${tabId}`,
        {
          layouts: layoutUpdates,
        }
      );
      return response.data;
    },
    onSuccess: (data, layoutUpdates) => {
      console.log('Layout saved successfully to backend');
      // Update the local boardBlocks state with the new layout data
      const updatedBlocks = boardBlocks.map((block) => {
        const layoutUpdate = layoutUpdates.find(
          (update) => update.blockId === block._id
        );
        if (layoutUpdate) {
          return {
            ...block,
            layout: layoutUpdate.layout,
          };
        }
        return block;
      });
      setReportBoards(updatedBlocks);

      // Update the cache for the current tab
      const currentTab = currentTabRef.current;
      setLayoutCache((prev) => ({
        ...prev,
        [currentTab]: currentLayouts,
      }));
    },
    onError: (error) => {
      console.error('Failed to save layout:', error);
      errorToast('Failed to save layout changes');
    },
  });

  // FIXED: Handle layout changes with proper race condition prevention
  const handleLayoutChange = useCallback(
    (layout: any[], allLayouts: any) => {
      // Prevent layout changes during editing, component initialization, or tab switching
      if (
        isEditingRef.current ||
        !isComponentReady ||
        isTabSwitchingRef.current
      ) {
        return;
      }

      // Ensure we're still on the same tab
      if (currentTabRef.current !== tabId) {
        return;
      }

      console.log('Layout changed for tab:', tabId);

      // Update local state immediately
      setCurrentLayouts(allLayouts);

      // Debounce save to prevent excessive API calls
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }

      saveTimeoutRef.current = setTimeout(() => {
        // Double-check we're still on the same tab before saving
        if (currentTabRef.current === tabId && !isTabSwitchingRef.current) {
          const layoutUpdates = layout.map((item) => ({
            blockId: item.i,
            layout: {
              x: item.x,
              y: item.y,
              w: item.w,
              h: item.h,
            },
          }));
          saveLayoutMutation.mutate(layoutUpdates);
        }
      }, 1000);
    },
    [isComponentReady, saveLayoutMutation, tabId]
  );

  // Check position occupation
  const isPositionOccupied = useCallback(
    (x: number, y: number, width = 6, height = 4) => {
      const currentLayout = currentLayouts.lg || [];
      return currentLayout.some((item: any) => {
        const itemRight = item.x + item.w;
        const itemBottom = item.y + item.h;
        const newRight = x + width;
        const newBottom = y + height;

        return !(
          x >= itemRight ||
          newRight <= item.x ||
          y >= itemBottom ||
          newBottom <= item.y
        );
      });
    },
    [currentLayouts.lg]
  );

  // Generate drop zones
  const generateDropZones = useCallback(() => {
    const zones: Array<{ x: number; y: number }> = [];
    const gridHeight = 20;
    const gridWidth = 12;
    const blockWidth = 6;
    const blockHeight = 4;

    for (let y = 0; y < gridHeight; y++) {
      for (let x = 0; x <= gridWidth - blockWidth; x += blockWidth) {
        if (!isPositionOccupied(x, y, blockWidth, blockHeight)) {
          zones.push({ x, y });
        }
      }
    }

    const currentLayout = currentLayouts.lg || [];
    currentLayout.forEach((item: any) => {
      const aboveY = Math.max(0, item.y - blockHeight);
      if (!isPositionOccupied(item.x, aboveY, blockWidth, blockHeight)) {
        zones.push({ x: item.x, y: aboveY });
      }

      const belowY = item.y + item.h;
      if (!isPositionOccupied(item.x, belowY, blockWidth, blockHeight)) {
        zones.push({ x: item.x, y: belowY });
      }

      const rightX = item.x + item.w;
      if (
        rightX <= gridWidth - blockWidth &&
        !isPositionOccupied(rightX, item.y, blockWidth, blockHeight)
      ) {
        zones.push({ x: rightX, y: item.y });
      }

      const leftX = Math.max(0, item.x - blockWidth);
      if (!isPositionOccupied(leftX, item.y, blockWidth, blockHeight)) {
        zones.push({ x: leftX, y: item.y });
      }
    });

    const uniqueZones = zones.filter(
      (zone, index, self) =>
        index === self.findIndex((z) => z.x === zone.x && z.y === zone.y)
    );

    return uniqueZones.slice(0, 10);
  }, [currentLayouts.lg, isPositionOccupied]);

  // Handle drag events
  useEffect(() => {
    const handleDragEnter = (e: DragEvent) => {
      if (isEditingRef.current) {
        e.preventDefault();
        e.stopPropagation();
        return;
      }
      e.preventDefault();
      setIsDragging(true);
      const zones = generateDropZones();
      setDropZones(zones);
    };

    const handleDragLeave = (e: DragEvent) => {
      if (isEditingRef.current) {
        e.preventDefault();
        e.stopPropagation();
        return;
      }
      e.preventDefault();
      if (
        !e.relatedTarget ||
        !(e.relatedTarget as Element).closest('.content-area')
      ) {
        setIsDragging(false);
        setDropZones([]);
        setPendingDropPosition(null);
      }
    };

    const handleDragEnd = () => {
      if (!isEditingRef.current) {
        setIsDragging(false);
        setDropZones([]);
        setPendingDropPosition(null);
        isProcessingDropRef.current = false;
      }
    };

    document.addEventListener('dragenter', handleDragEnter);
    document.addEventListener('dragleave', handleDragLeave);
    document.addEventListener('dragend', handleDragEnd);

    return () => {
      document.removeEventListener('dragenter', handleDragEnter);
      document.removeEventListener('dragleave', handleDragLeave);
      document.removeEventListener('dragend', handleDragEnd);
    };
  }, [generateDropZones]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
      if (layoutUpdateTimeoutRef.current) {
        clearTimeout(layoutUpdateTimeoutRef.current);
      }
      // NEW: Cleanup loading overlay timeout
      if (loadingOverlayTimeoutRef.current) {
        clearTimeout(loadingOverlayTimeoutRef.current);
      }
    };
  }, []);

  // Create board block mutation
  const mutateCreateBoardBlock = useMutation({
    mutationFn: async (data: any) => {
      // NEW: Show loading overlay if this is the first block being added
      if (isFirstContentAddition()) {
        showLoadingOverlayWithTimeout(
          'Please wait a moment to get layout ready'
        );
      }

      console.log('API call - creating block with data:', data);
      const res = await api.post(
        `/board/board-blocks/${reportId}/${tabId}`,
        data
      );
      return res.data;
    },
    onSuccess: (data) => {
      const createdBlock = data?.data;
      console.log('Block created successfully:', createdBlock);

      if (
        createdBlock.type === 'custom_chart' &&
        createdBlock.customChartType === 'section'
      ) {
        const selectedBlockIds =
          createdBlock.customChartConfig?.selectedBlocks || [];
        const updatedBlocks = boardBlocks.map((block) => {
          if (selectedBlockIds.includes(block._id)) {
            return { ...block, parentSection: createdBlock._id };
          }
          return block;
        });
        setReportBoards([...updatedBlocks, createdBlock]);
      } else {
        if (createdBlock.type === 'image' || createdBlock.type === 'video') {
          updateImageWithKey.mutate({
            key: createdBlock?.mediaId,
            id: createdBlock?._id,
            type:
              createdBlock.type == 'image'
                ? 'board_block_image'
                : 'board_block_video',
          });
        }

        if (
          createdBlock.customChartType === 'author_block' ||
          createdBlock.customChartType === 'company_block'
        ) {
          updateImageWithKey.mutate({
            key: createdBlock?.customChartConfig?.imageKey,
            id: createdBlock?._id,
            type:
              createdBlock.customChartType == 'author_block'
                ? 'board_block_author_image'
                : 'board_block_company_image',
          });
        }

        if (createdBlock.customChartType === 'testimonial_block') {
          updateImageWithKey.mutate({
            key: createdBlock?.customChartConfig?.imageKey,
            id: createdBlock?._id,
            type: 'bb_testimonial_author',
          });
          updateImageWithKey.mutate({
            key: createdBlock?.customChartConfig?.companyImgKey,
            id: createdBlock?._id,
            type: 'bb_testimonial_company',
          });
        }

        setReportBoards([...boardBlocks, createdBlock]);
      }

      successToast('Block added successfully');
      setIsAddCustomCharBlockModalOpen(false);
      isProcessingDropRef.current = false;
      setPendingDropPosition(null);
    },
    onError: (error: any) => {
      console.error('Block creation failed:', error);
      errorToast(error?.response?.data?.message || 'Failed to create block');
      isProcessingDropRef.current = false;
      setPendingDropPosition(null);
    },
  });

  // Use template mutation
  const mutateUseTemplate = useMutation({
    mutationFn: async (templateId: any) => {
      // NEW: Show loading overlay for 5 seconds if this is the first content
      if (isFirstContentAddition()) {
        showLoadingOverlayWithTimeout(
          'Please wait a moment to get layout ready',
          5000
        );
      }

      const res = await api.post(
        `/board/board-blocks/board-templates/use-template/${reportId}/${tabId}/${templateId}`
      );
      return res.data;
    },
    onSuccess: (data) => {
      const newBlocks = data?.data;
      console.log({ newBlocks }, 'template used');
      successToast('Template used successfully');
      setReportBoards([...newBlocks]);
      setIsTemplateOpen(false);
    },
    onError: (error: any) => {
      console.error('Template using failed:', error);
      errorToast(error?.response?.data?.message || 'Failed to use template');
    },
  });

  // Update image with key mutation
  const updateImageWithKey = useMutation({
    mutationFn: async (payload: any) => {
      const res = await api.post('/media/update-image-with-key', {
        key: payload.key,
        actionType: 'push',
        options: {
          type: payload?.type,
          id: payload?.id,
        },
      });
      return res.data.data;
    },
    onSuccess: (data) => {},
  });

  // Handle drop from sidebar at specific position
  const handleDropAtPosition = useCallback(
    (component: any, gridPosition: { x: number; y: number }) => {
      setIsSidebarOpen(false);

      if (isProcessingDropRef.current) {
        console.log('Drop blocked - already processing');
        return;
      }

      console.log('handleDropAtPosition called with:', {
        component,
        gridPosition,
      });

      isProcessingDropRef.current = true;
      setPendingDropPosition(gridPosition);

      let data: any = null;
      setSelectedBlockType(component.type);

      switch (component.type) {
        case 'text':
          data = {
            type: 'text',
            content:
              'Edit your content here! This is a sample text that demonstrates how the block will automatically adjust its height based on the content length. You can add more text and the block will grow accordingly.',
          };
          break;

        case 'whats_inside': {
          const hasWhatsInside = boardBlocks?.find(
            (bb) => bb.type === 'whats_inside'
          );
          if (hasWhatsInside) {
            setIsDragging(false);
            setDropZones([]);
            errorToast("You have already added what's inside block!");
            return;
          }
          data = {
            type: 'whats_inside',
          };
          break;
        }

        case 'custom_chart':
          setSelectedCustomChartType(component.customChartType);
          setIsAddCustomCharBlockModalOpen(true);
          setIsDragging(false);
          setDropZones([]);
          return;

        case 'image':
        case 'video':
          setIsAddMediaBlockSheetOpen(true);
          setIsDragging(false);
          setDropZones([]);
          return;
      }

      if (data) {
        const tempBlock = { ...data, _id: 'temp' } as TBoardBlock;
        const dynamicHeight = calculateContentHeight(tempBlock, false); // Don't preserve for new blocks

        data.layout = {
          x: gridPosition.x,
          y: gridPosition.y,
          w: 6,
          h: dynamicHeight,
        };

        console.log('Creating block with data:', data);
        mutateCreateBoardBlock.mutate({ ...data });
      }

      setIsDragging(false);
      setDropZones([]);
    },
    [calculateContentHeight, mutateCreateBoardBlock, boardBlocks]
  );

  const onCloseCustomChartCreateModal = () => {
    setSelectedCustomChartType(undefined);
    setIsAddCustomCharBlockModalOpen(false);
    isProcessingDropRef.current = false;
    setPendingDropPosition(null);
  };

  const onConfirmImageVideoUpload = (file: any) => {
    const requestData = {
      type: selectedBlockType,
      mediaKey: file?.key,
      layout: pendingDropPosition
        ? {
            x: pendingDropPosition.x,
            y: pendingDropPosition.y,
            w: 6,
            h: calculateContentHeight(
              {
                type: selectedBlockType,
              } as TBoardBlock,
              false
            ), // Don't preserve for new blocks
          }
        : undefined,
    };

    mutateCreateBoardBlock.mutate(requestData);
  };

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Enhanced render logic
  const renderContent = () => {
    if (loading || !isComponentReady) {
      return (
        <div className="mx-auto">
          <BoardSkeleton />
        </div>
      );
    }

    if (topLevelBlocks && topLevelBlocks.length > 0) {
      return (
        <div className="mx-auto">
          <div className="relative">
            {isDragging &&
              dropZones.map((zone, index) => (
                <DropZone
                  key={`${zone.x}-${zone.y}-${index}`}
                  onDrop={handleDropAtPosition}
                  gridPosition={zone}
                  isVisible={true}
                  width={6}
                  height={4}
                />
              ))}

            <ResponsiveGridLayout
              ref={gridInstanceRef}
              className={`layout ${!isEditMode ? 'edit-mode-disabled' : ''} ${
                isEditingRef.current ? 'editing-locked' : ''
              }`}
              layouts={currentLayouts}
              onLayoutChange={handleLayoutChange}
              cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
              breakpoints={{
                lg: 1200,
                md: 996,
                sm: 768,
                xs: 480,
                xxs: 0,
              }}
              rowHeight={40}
              isDraggable={isEditMode && !isDragging && !isEditingRef.current}
              isResizable={isEditMode && !isDragging && !isEditingRef.current}
              margin={[16, 16]}
              useCSSTransforms={false}
              preventCollision={true}
              compactType={null}
              allowOverlap={false}
              // @ts-ignore
              dragHandleClassName="drag-handle"
              draggableCancel=".text-content-editable,[contenteditable='true'],.bn-editor,.mantine-Editor-root,.bn-block-content,.bn-inline-content,.bn-formatting-toolbar,.bn-side-menu,.bn-slash-menu,button,[role='button'],input,select,textarea,[data-bn-type],[data-content-type],.edit-popover,.edit-modal"
              resizeHandles={['se', 'sw', 'ne', 'nw', 'n', 's', 'e', 'w']}
              key={`stable-grid-${tabId}`}
            >
              {topLevelBlocks.map((block) => (
                <div
                  key={block._id}
                  className={
                    editingBlockId === block._id ? 'editing-active' : ''
                  }
                  // @ts-ignore
                  data-block-type={block.customChartType || block.type}
                >
                  <div
                    className={`board-block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full flex-block-container ${
                      editingBlockId === block._id ? 'editing-active' : ''
                    } ${isEditMode ? 'edit-mode-active' : ''}`}
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      border: isEditMode
                        ? '2px dashed rgba(34, 197, 94, 0.3)'
                        : undefined,
                      borderRadius: isEditMode ? '8px' : undefined,
                    }}
                  >
                    <SingleBoardBlock
                      // @ts-ignore
                      allProps={{
                        block,
                        boardBlocks,
                        setReportBoards,
                        editingBlockId,
                        setEditingBlockId: setEditingBlockIdSafe,
                      }}
                    />
                  </div>
                </div>
              ))}
            </ResponsiveGridLayout>
          </div>
        </div>
      );
    }

    if (topLevelBlocks && topLevelBlocks.length === 0) {
      if (isDragging) {
        return (
          <div className="mx-auto">
            <div className="relative">
              <DropZone
                onDrop={handleDropAtPosition}
                gridPosition={{ x: 0, y: 0 }}
                isVisible={true}
                width={6}
                height={4}
              />
              {dropZones.map((zone, index) => (
                <DropZone
                  key={`${zone.x}-${zone.y}-${index}`}
                  onDrop={handleDropAtPosition}
                  gridPosition={zone}
                  isVisible={true}
                  width={6}
                  height={4}
                />
              ))}
            </div>
          </div>
        );
      }

      if (showEmptyState) {
        return isTemplateOpen ? (
          <AllBoardTemplates
            reportId={reportId}
            tabId={tabId}
            mutateUseTemplate={mutateUseTemplate}
          />
        ) : (
          <BoardEmptyState
            setIsSidebarOpen={setIsSidebarOpen}
            setIsTemplateOpen={setIsTemplateOpen}
          />
        );
      }
    }

    return (
      <div className="mx-auto">
        <BoardSkeleton />
      </div>
    );
  };

  return (
    <EditingContext.Provider
      value={{ editingBlockId, setEditingBlockId: setEditingBlockIdSafe }}
    >
      <div className="flex h-full flex-col bg-gray-100 relative">
        {/* NEW: Loading Overlay */}
        <LoadingOverlay
          isVisible={showLoadingOverlay}
          message="Please wait a moment to get layout ready"
        />

        <div className="flex flex-1 h-full">
          {showEmptyState ? (
            <>
              {' '}
              {!viewOnly && !isInsight && (
                <CustomSidebar
                  isOpen={isSidebarOpen}
                  onToggle={toggleSidebar}
                />
              )}
            </>
          ) : (
            <>
              {!viewOnly && !isInsight && isEditMode && (
                <CustomSidebar
                  isOpen={isSidebarOpen}
                  onToggle={toggleSidebar}
                />
              )}
            </>
          )}

          {/* {!viewOnly && !isInsight && isEditMode && (
            <CustomSidebar isOpen={isSidebarOpen} onToggle={toggleSidebar} />
          )} */}

          <div
            ref={contentAreaRef}
            className={`content-area flex-1 h-[89.5vh] overflow-y-auto pb-10 bg-[#EFF5F6] px-4 pt-4 transition-all duration-300 ease-in-out ${
              isDragging ? 'dragging' : ''
            }`}
          >
            <BrandPerformanceHeader
              reportId={reportId}
              tabId={tabId}
              contentAreaRef={contentAreaRef}
              currentBoard={currentBoard}
              setCurrentBoard={setCurrentBoard}
              isEditMode={isEditMode}
              setIsEditMode={setIsEditMode}
              isTemplateOpen={isTemplateOpen}
              setIsTemplateOpen={setIsTemplateOpen}
            />
            {renderContent()}
          </div>
        </div>

        <CreateCustomChartBlockModal
          boardBlocks={boardBlocks}
          type={selectedCustomChartType}
          isOpen={isAddCustomCharBlockModalOpen}
          onClose={onCloseCustomChartCreateModal}
          mutateCreateBoardBlock={{
            mutate: (data: any) => {
              const requestData = {
                ...data,
                layout: pendingDropPosition
                  ? {
                      x: pendingDropPosition.x,
                      y: pendingDropPosition.y,
                      w: 6,
                      h: calculateContentHeight(
                        {
                          type: 'custom_chart',
                          customChartType: selectedCustomChartType,
                          ...data,
                        } as TBoardBlock,
                        false
                      ), // Don't preserve for new blocks
                    }
                  : undefined,
              };
              mutateCreateBoardBlock.mutate(requestData);
            },
            // @ts-ignore
            isLoading: mutateCreateBoardBlock.isPending,
          }}
        />

        <AddMediaBlockSheet
          openDrawer={isAddMediaBlockSheetOpen}
          setOpenDrawer={(open) => {
            setIsAddMediaBlockSheetOpen(open);
            if (!open) {
              isProcessingDropRef.current = false;
              setPendingDropPosition(null);
            }
          }}
          onSave={onConfirmImageVideoUpload}
          type={selectedBlockType as 'image' | 'video'}
        />
      </div>
    </EditingContext.Provider>
  );
}

export { EditingContext };
