'use client';

import PrimaryButton from '@/components/Common/buttons/primary-button';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import api from '@/lib/api';
import { useQuery } from '@tanstack/react-query';
import { Layout, Maximize2, X } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import ChartBoardBlock from '../Blocks/ChartBoardBlock';
import CustomChartBoardBlock from '../Blocks/CustomChartBlock/CustomChartBlock';
import ImageBoardBlock from '../Blocks/ImageBoardBlock';
import TextBoardBlock from '../Blocks/TextBoardBlock';
import TitleBoardBlock from '../Blocks/TitleBoardBlock';
import VideoBoardBlock from '../Blocks/VideoBoardBlock';
import WhatsInsideBoardBlock from '../Blocks/WhatsInsideBoardBlock';
import TemplatesSkeleton from './TemplatesSkeleton';

const ResponsiveGridLayout = WidthProvider(Responsive);

const renderBlock = (
  block: any,
  boardBlocks: any[],
  setReportBoards: any,
  reportId: any,
  tabId: any
) => {
  const commonProps = {
    block,
    boardBlocks,
    setReportBoards,
    reportId,
    tabId,
  };

  switch (block.type) {
    case 'title':
      return <TitleBoardBlock {...commonProps} />;
    case 'text':
      return <TextBoardBlock {...commonProps} />;
    case 'chart':
      return <ChartBoardBlock {...commonProps} />;
    case 'image':
      return <ImageBoardBlock {...commonProps} />;
    case 'video':
      return <VideoBoardBlock {...commonProps} />;
    case 'whats_inside':
      return <WhatsInsideBoardBlock {...commonProps} />;
    case 'custom_chart':
      return <CustomChartBoardBlock {...commonProps} />;
    default:
      return (
        <div className="flex items-center justify-center h-full text-gray-400">
          Unknown block type
        </div>
      );
  }
};

const TemplatePreviewModal = ({
  template,
  isOpen,
  onClose,
  reportId,
  onUseTemplate,
  isLoading,
}: {
  template: any;
  isOpen: boolean;
  onClose: () => void;
  reportId: string;
  onUseTemplate: (template: any) => void;
  isLoading: boolean;
}) => {
  const [isModalReady, setIsModalReady] = useState(false);
  const [currentLayouts, setCurrentLayouts] = useState<any>({
    lg: [],
    md: [],
    sm: [],
    xs: [],
    xxs: [],
  });

  const {
    data,
    isLoading: isBlocksLoading,
    error,
  } = useQuery({
    queryKey: ['single-template-blocks', template?._id],
    queryFn: async () => {
      const res = await api.get(
        `/board/board-blocks/board-templates/get-single-template-blocks/${template?._id}`
      );
      return res?.data;
    },
    retryOnMount: false,
    refetchOnWindowFocus: false,
    retry: 3,
    enabled: !!template?._id && isOpen,
  });

  // Calculate dynamic height based on content - same logic as main board
  const calculateContentHeight = useCallback((block: any) => {
    const baseHeight = 4;

    // Always preserve existing height if it exists
    if (block.layout?.h && block.layout.h > 0) {
      return block.layout.h;
    }

    switch (block.type) {
      case 'title': {
        const title = block.title || 'This is your title, edit here!';
        const estimatedLines = Math.ceil(title.length / 50);
        return Math.max(baseHeight, Math.ceil(estimatedLines * 0.4) + 1);
      }
      case 'text': {
        const content = block.content || 'Edit your content here!';
        const estimatedLines = Math.ceil(content.length / 80);
        return Math.max(baseHeight + 1, Math.ceil(estimatedLines * 0.3) + 2);
      }
      case 'chart':
      case 'custom_chart': {
        if (block?.customChartType === 'overview_block') {
          return Math.max(2, baseHeight);
        } else if (block?.customChartType === 'section') {
          if (block.layout?.h && block.layout.h > 0) {
            return block.layout.h;
          }
          const selectedBlocks = block?.customChartConfig?.selectedBlocks || [];
          const rows = Math.ceil(selectedBlocks.length / 3);
          const blocksHeight = rows * 4;
          const headerHeight = 3;
          const padding = 2;
          return Math.max(6, headerHeight + blocksHeight + padding);
        } else {
          return Math.max(6, baseHeight + 6);
        }
      }
      case 'image':
      case 'video': {
        return Math.max(5, baseHeight + 3);
      }
      default:
        return baseHeight + 2;
    }
  }, []);

  // Generate layouts from blocks - same logic as main board
  const generateLayoutForBlocks = useCallback(
    (blocks: any[]) => {
      if (!blocks || blocks.length === 0) return [];

      return blocks.map((block, index) => {
        // CRITICAL: ALWAYS use backend layout data if it exists
        if (block.layout && typeof block.layout === 'object') {
          return {
            i: block._id,
            x: block.layout.x ?? 0,
            y: block.layout.y ?? 0,
            w: block.layout.w ?? 6,
            h: block.layout.h ?? 4,
            minW: 3,
            minH:
              block.type === 'custom_chart' &&
              block.customChartType === 'section'
                ? 6
                : 2,
            maxW: 12,
            maxH: 50,
            static: true, // Make blocks non-interactive in preview
          };
        }

        // Only calculate if no backend layout exists
        const height = calculateContentHeight(block);
        return {
          i: block._id,
          x: (index % 2) * 6,
          y: Math.floor(index / 2) * height,
          w: 6,
          h: height,
          minW: 3,
          minH:
            block.type === 'custom_chart' && block.customChartType === 'section'
              ? 6
              : 2,
          maxW: 12,
          maxH: 50,
          static: true, // Make blocks non-interactive in preview
        };
      });
    },
    [calculateContentHeight]
  );

  // Update layouts when blocks are loaded
  useEffect(() => {
    if (data?.data && isOpen) {
      const blocks = data.data;
      const topLevelBlocks = blocks.filter(
        (block: any) => !block.parentSection
      );

      if (topLevelBlocks.length > 0) {
        const defaultLayout = generateLayoutForBlocks(topLevelBlocks);
        const newLayouts = {
          lg: defaultLayout,
          md: defaultLayout.map((item) => ({
            ...item,
            w: Math.min(item.w, 6),
          })),
          sm: defaultLayout.map((item) => ({
            ...item,
            w: Math.min(item.w, 6),
          })),
          xs: defaultLayout.map((item) => ({ ...item, w: 4, x: 0 })),
          xxs: defaultLayout.map((item) => ({ ...item, w: 2, x: 0 })),
        };

        setCurrentLayouts(newLayouts);

        // Set modal ready after a short delay to ensure layout is applied
        setTimeout(() => {
          setIsModalReady(true);
        }, 100);
      }
    }
  }, [data, isOpen, generateLayoutForBlocks]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setIsModalReady(false);
      setCurrentLayouts({
        lg: [],
        md: [],
        sm: [],
        xs: [],
        xxs: [],
      });
    }
  }, [isOpen]);

  if (!template) return null;

  const blocks = data?.data;
  const topLevelBlocks = blocks?.filter((block: any) => !block.parentSection);

  if (error) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <div className="text-red-500 mb-4">
              <Layout className="w-12 h-12 mx-auto mb-2" />
              <h3 className="text-lg font-semibold">Failed to load template</h3>
              <p className="text-sm text-gray-600">Please try again later</p>
            </div>
            <Button onClick={onClose} variant="outline">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (isBlocksLoading || !isModalReady) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-none w-screen h-screen m-0 p-0 rounded-none overflow-hidden">
          <DialogHeader className="flex-shrink-0 px-6 py-4 border-b bg-white z-10 relative">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 text-blue-700">
                  <Maximize2 className="w-5 h-5" />
                </div>
                <div>
                  <DialogTitle className="text-xl font-semibold text-gray-900">
                    {template.name} - Preview
                  </DialogTitle>
                  <p className="text-sm text-gray-600 mt-1">
                    Loading template...
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={onClose}
                className="p-2 bg-transparent"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </DialogHeader>
          <div className="flex-1 bg-[#EFF5F6] overflow-hidden">
            <div className="p-6">
              <TemplatesSkeleton />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-none w-screen h-screen m-0 p-0 rounded-none overflow-hidden">
        {/* Header */}
        <DialogHeader className="flex-shrink-0 px-6 py-4 border-b bg-white z-10 relative">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-100 text-blue-700">
                <Maximize2 className="w-5 h-5" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold text-gray-900">
                  {template.name} - Preview
                </DialogTitle>
                <p className="text-sm text-gray-600 mt-1">
                  {template.description}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <PrimaryButton
                onClick={() => {
                  onUseTemplate(template);
                  onClose();
                }}
                disabled={isLoading}
                className="flex items-center text-white"
              >
                {isLoading ? (
                  <>
                    <svg
                      className="animate-spin w-4 h-4 mr-2 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      />
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                      />
                    </svg>
                    Loading...
                  </>
                ) : (
                  <>
                    <Layout className="w-4 h-4 mr-2" />
                    Use Template
                  </>
                )}
              </PrimaryButton>
              <Button
                variant="outline"
                size="sm"
                onClick={onClose}
                className="p-2 bg-transparent"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Scrollable Content Area */}
        <div
          className="flex-1 bg-[#EFF5F6] overflow-y-auto overflow-x-hidden"
          style={{
            height: 'calc(100vh - 140px)',
            maxHeight: 'calc(100vh - 140px)',
          }}
        >
          <div className="p-6">
            <div className="mx-auto">
              <style jsx>{`
                .react-grid-layout {
                  position: relative;
                }
                .react-grid-item {
                  transition: all 200ms ease;
                  transition-property: left, top;
                }
                .react-grid-item.cssTransforms {
                  transition-property: transform;
                }
                .board-block {
                  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
                    0 2px 4px -1px rgba(0, 0, 0, 0.06);
                  transition: all 0.2s ease-in-out;
                }
                .preview-modal-grid {
                  pointer-events: none;
                }
                .preview-modal-grid .board-block {
                  border: 1px solid rgba(0, 0, 0, 0.1);
                }
                .preview-modal-grid .react-grid-item {
                  transition: none;
                }
                .preview-modal-grid button,
                .preview-modal-grid input,
                .preview-modal-grid select,
                .preview-modal-grid textarea {
                  pointer-events: none;
                  opacity: 0.9;
                }
                /* Ensure blocks are positioned correctly */
                .preview-modal-grid .react-grid-item {
                  position: absolute !important;
                }
                /* Custom scrollbar */
                .modal-scroll-container::-webkit-scrollbar {
                  width: 8px;
                }
                .modal-scroll-container::-webkit-scrollbar-track {
                  background: #f1f1f1;
                  border-radius: 4px;
                }
                .modal-scroll-container::-webkit-scrollbar-thumb {
                  background: #c1c1c1;
                  border-radius: 4px;
                }
                .modal-scroll-container::-webkit-scrollbar-thumb:hover {
                  background: #a8a8a8;
                }
              `}</style>

              <ResponsiveGridLayout
                className="layout preview-modal-grid"
                layouts={currentLayouts}
                cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
                breakpoints={{
                  lg: 1200,
                  md: 996,
                  sm: 768,
                  xs: 480,
                  xxs: 0,
                }}
                rowHeight={40} // Same as main board
                isDraggable={false}
                isResizable={false}
                margin={[16, 16]} // Same as main board
                useCSSTransforms={false} // Same as main board
                preventCollision={true}
                compactType={null} // Same as main board
                allowOverlap={false}
                autoSize={true}
                key={`template-preview-${template._id}`} // Unique key for each template
              >
                {topLevelBlocks?.map((block: any) => (
                  <div key={block._id}>
                    <div className="board-block bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden h-full flex-block-container">
                      <div className="flex-1 overflow-hidden min-h-0">
                        <div className="w-full h-full">
                          {renderBlock(
                            block,
                            blocks || [],
                            undefined,
                            reportId,
                            undefined
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </ResponsiveGridLayout>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex-shrink-0 px-6 py-4 bg-white border-t z-10 relative">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <div className="flex items-center gap-6">
              <span className="flex items-center gap-2">
                <Layout className="w-4 h-4" />
                {template.blocks?.length || 0} blocks
              </span>
              <span className="px-2 py-1 bg-gray-100 rounded-md text-xs">
                {template.privacy === 'only_me'
                  ? 'Only Me'
                  : template.privacy === 'my_team'
                  ? 'My Team'
                  : template.privacy === 'si_team'
                  ? 'SI Team'
                  : 'Public'}
              </span>
            </div>
            <div className="text-xs text-gray-500">
              Press ESC to close or click the X button
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TemplatePreviewModal;
