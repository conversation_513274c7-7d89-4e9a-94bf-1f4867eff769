// /** @type {import('next').NextConfig} */
// const webpack = require('webpack');
// const path = require('path');

// const nextConfig = {
//   images: {
//     remotePatterns: [
//       {
//         protocol: 'https',
//         hostname: '**',
//       },
//     ],
//   },
//   swcMinify: true,
//   reactStrictMode: false,
//   productionBrowserSourceMaps: false,
//   webpack: (config, { dev, isServer }) => {
//     if (dev) {
//       config.optimization.splitChunks = {
//         chunks: 'all',
//       };
//     }

//     if (config.cache && !dev) {
//       config.cache = Object.freeze({
//         type: 'memory',
//       });
//     }

//     // Fix Webpack ES module issues
//     config.module.rules.push({
//       test: /\.mjs$/,
//       include: /node_modules/,
//       type: 'javascript/auto',
//     });

//     config.resolve.alias.lottie = 'lottie-web/build/player/lottie_light.js';

//     config.plugins.push(
//       new webpack.ContextReplacementPlugin(
//         /@hugocxl[\\/]react-to-image[\\/]dist/,
//         path.resolve(__dirname, 'node_modules/@hugocxl/react-to-image/dist')
//       )
//     );

//     return config;
//   },
//   transpilePackages: ['lottie-web'],
//   compiler: {
//     removeConsole: process.env.NODE_ENV === 'production',
//   },
//   experimental: {
//     optimizePackageImports: [
//       '@phosphor-icons/react',
//       '@radix-ui/react-icons',
//       'media-icons',
//       'react-icons',
//       // 'primereact',
//       // 'preact',
//       // '@headlessui/react',
//       // '@chakra-ui/react',
//     ],
//     webpackBuildWorker: true,
//     serverSourceMaps: false,
//   },
// };

// const withBundleAnalyzer = require('@next/bundle-analyzer')({
//   enabled: process.env.ANALYZE === 'true',
// });

// module.exports = withBundleAnalyzer(nextConfig);
// // module.exports = nextConfig;

const isDev = process.env.NODE_ENV === 'development';

/** @type {import('next').NextConfig} */
const webpack = require('webpack');
const path = require('path');
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
    unoptimized: true,
  },
  swcMinify: true, // You already have SWC minification enabled which is good for faster builds.
  reactStrictMode: false, // You can keep this false to avoid React Strict Mode overhead.
  productionBrowserSourceMaps: false, // Disabling source maps in production to speed up builds.
  webpack: (config, { dev, isServer }) => {
    if (dev) {
      // In development, optimize splitChunks to ensure faster rebuilds
      config.optimization.splitChunks = {
        chunks: 'all',
        maxInitialRequests: 5, // Limit the number of initial requests to speed up dev reloads
        maxAsyncRequests: 5, // Limit async requests to reduce build times
      };
      config.cache = false; // Disable caching in development mode
    }
    // Cache setup for production
    if (config.cache && !dev) {
      config.cache = {
        type: 'memory', // Using memory cache for production builds to speed things up.
      };
    }
    // Fix Webpack ES module issues (mjs files)
    config.module.rules.push({
      test: /\.mjs$/,
      include: /node_modules/,
      type: 'javascript/auto',
    });
    // Aliasing lottie to lottie_light.js for better performance
    config.resolve.alias.lottie = 'lottie-web/build/player/lottie_light.js';
    // Optimize Webpack for large libraries by ensuring unnecessary parts don't get bundled in dev.
    config.plugins.push(
      new webpack.ContextReplacementPlugin(
        /@hugocxl[\\/]react-to-image[\\/]dist/,
        path.resolve(__dirname, 'node_modules/@hugocxl/react-to-image/dist')
      )
    );
    return config;
  },
  transpilePackages: ['lottie-web'], // Ensure that lottie-web is transpiled properly.
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production', // Remove console logs in production.
  },
  experimental: !isDev
    ? {
        optimizePackageImports: [
          '@phosphor-icons/react',
          '@radix-ui/react-icons',
          'media-icons',
          'react-icons',
          // 'primereact',
          // 'preact',
          // '@headlessui/react',
          // '@chakra-ui/react',
        ], // Automatically optimize imports for these packages
        webpackBuildWorker: true, // Disable Webpack build workers to avoid extra overhead in dev mode.
        serverSourceMaps: false, // Disable server-side source maps for faster builds.
      }
    : {
        optimizePackageImports: [
          '@phosphor-icons/react',
          '@radix-ui/react-icons',
          'media-icons',
          'react-icons',
          'primereact',
          'preact',
          '@headlessui/react',
          '@chakra-ui/react',
        ], // Automatically optimize imports for these packages
        webpackBuildWorker: false, // Disable Webpack build workers to avoid extra overhead in dev mode.
        serverSourceMaps: false, // Disable server-side source maps for faster builds.
      },

  // Optional: Adjust the file watcher limit to avoid issues in large projects
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: '/api/:path*',
      },
    ];
  },
};
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'false',
});
module.exports = withBundleAnalyzer(nextConfig);
