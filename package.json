{"name": "standard-insights-client", "description": "Get Better Insights Faster", "author": {"name": "Standard Insights", "email": "<EMAIL>", "url": "https://standard-insights.com"}, "version": "2.4.12", "private": true, "scripts": {"inspect-dev": "cross-env NODE_OPTIONS='--inspect' next dev", "dev": "next dev", "start": "next start", "build": "next build", "build-analyze": "cross-env ANALYZE=true next build --experimental-debug-memory-usage", "lint": "next lint", "prepare": "husky install", "prettier": "prettier --write .", "postinstall": "patch-package", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "text:codegen": "npx playwright codegen http://localhost:3000"}, "dependencies": {"@blocknote/core": "^0.30.0", "@blocknote/mantine": "^0.30.0", "@blocknote/react": "^0.30.0", "@blocknote/xl-multi-column": "^0.30.0", "@bprogress/next": "^3.2.12", "@chakra-ui/next-js": "^2.1.5", "@chakra-ui/react": "^2.8.1", "@cyntler/react-doc-viewer": "^1.17.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@headlessui/react": "^1.7.19", "@hugocxl/react-to-image": "^0.0.9", "@kurkle/color": "^0.3.4", "@mistralai/mistralai": "^0.5.0", "@next/bundle-analyzer": "^15.1.7", "@next/third-parties": "^15.2.1", "@oplayer/core": "^1.2.37", "@oplayer/ui": "^1.3.3", "@phosphor-icons/react": "^2.1.7", "@preact/signals": "^1.3.2", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@react-oauth/google": "^0.11.1", "@smastrom/react-rating": "^1.5.0", "@stripe/react-stripe-js": "^2.9.0", "@stripe/stripe-js": "^2.4.0", "@tanstack/query-sync-storage-persister": "^4.35.0", "@tanstack/react-query": "^4.26.1", "@tanstack/react-query-devtools": "^4.36.1", "@tanstack/react-query-persist-client": "^4.35.0", "@tanstack/react-table": "^8.21.2", "@types/react-grid-layout": "^1.3.5", "@uidotdev/usehooks": "^2.4.1", "@vidstack/react": "^1.12.12", "apexcharts": "^4.4.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "chart.js": "^3.9.1", "chartjs-chart-geo": "^3.10.0", "chartjs-plugin-datalabels": "^2.2.0", "chartjs-plugin-watermark": "^2.0.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "disable-devtool": "^0.3.8", "draft-js": "^0.11.7", "draftjs-to-html": "^0.9.1", "draftjs-utils": "^0.10.2", "dropbox": "^10.34.0", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "file-saver": "^2.0.5", "firebase": "^9.23.0", "framer-motion": "^7.10.3", "html-to-draftjs": "^1.5.0", "html-to-image": "^1.11.12", "html2canvas": "^1.4.1", "input-otp": "^1.4.2", "jodit-react": "^4.1.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jszip": "^3.10.1", "lancaster-stemmer": "^2.1.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "lucide-react": "^0.378.0", "mammoth": "^1.9.0", "media-icons": "^1.1.5", "moment": "^2.30.1", "next": "^14.2.25", "next-themes": "^0.4.4", "patch-package": "^8.0.0", "payment": "^2.4.7", "pptxgenjs": "^3.12.0", "preact": "^10.25.4", "primereact": "^10.9.2", "qrcode.react": "^3.2.0", "react": "^18.3.1", "react-advanced-cropper": "^0.20.0", "react-apexcharts": "^1.7.0", "react-beautiful-dnd": "^13.1.1", "react-calendly": "^4.3.1", "react-chartjs-2": "^4.3.1", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-countup": "^6.5.3", "react-csv": "^2.2.2", "react-data-grid": "^7.0.0-beta.47", "react-datepicker": "^5.1.0", "react-day-picker": "^8.10.1", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-draft-wysiwyg": "^1.15.0", "react-dropzone": "^14.3.8", "react-easy-crop": "^5.2.0", "react-grid-gallery": "^1.0.1", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.54.2", "react-icons": "^4.12.0", "react-image-crop": "^11.0.10", "react-markdown": "^9.0.3", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.11", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-resizable": "^3.0.5", "react-resizable-panels": "^3.0.2", "react-responsive": "^10.0.1", "react-scroll": "^1.9.3", "react-select": "^5.10.0", "react-syntax-highlighter": "^15.6.1", "react-tabs-scrollable": "^1.0.10", "react-ui-scrollspy": "^2.3.0", "react-use": "^17.6.0", "recharts": "^2.15.1", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sharp": "^0.31.3", "sonner": "^1.7.4", "srt-parser-2": "^1.2.3", "stemmer": "^2.0.1", "swiper": "^11.2.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "webfontloader": "^1.6.28", "world-atlas": "^2.0.2", "xlsx": "^0.18.5", "yet-another-react-lightbox": "^3.21.7", "zustand": "^4.5.6"}, "devDependencies": {"@babel/core": "^7.26.8", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@next/eslint-plugin-next": "^14.2.24", "@playwright/test": "^1.53.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/chart.js": "^2.9.41", "@types/draft-js": "^0.11.18", "@types/draftjs-to-html": "^0.8.4", "@types/html-to-draftjs": "^1.5.0", "@types/node": "24.0.1", "@types/react": "^18.3.18", "@types/react-color": "^3.0.13", "@types/react-dom": "^18.3.5", "@types/react-draft-wysiwyg": "^1.13.8", "@types/react-scroll": "^1.8.10", "@types/react-select": "^5.0.0", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^8.35.0", "autoprefixer": "^10.4.20", "babel-loader": "^8.4.1", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-next": "^14.2.24", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^4.3.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.37.4", "husky": "^8.0.3", "postcss": "^8.5.2", "prettier": "^2.8.8", "tailwindcss": "^3.4.17", "typescript": "^4.9.5", "webpack": "^5.97.1"}, "resolutions": {"webpack": "^5"}}