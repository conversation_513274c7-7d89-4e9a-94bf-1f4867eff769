'use client';

import useAuth from '@/Auth/useAuth';
import Disable from '@/components/DisableDevtool';
import BusinessDetails from '@/components/Signupnew/BusinessDetails';
import OnboardingCall from '@/components/Signupnew/OnboardingCall';
import PrimaryGoal from '@/components/Signupnew/PrimaryGoal';
import ProjectDetails from '@/components/Signupnew/ProjectDetails';
import SignupDetails from '@/components/Signupnew/SignupDetails';
import styles from '@/styles/Signup.module.css';
import { AnimatePresence, motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

const SignupNew = () => {
  const router = useRouter();
  const [step, setStep] = useState<number>(1);
  const [signUpData, setSignUpData] = useState<any>({});
  const { user, loading } = useAuth();

  useEffect(() => {
    if (user && !loading) {
      router.push('/');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (typeof window !== undefined) {
      document.title = 'Register - Standard Insights';
    }
  }, []);

  return (
    <div
      className={`${styles.signup__wrapper} relative flex justify-center  w-full mx-auto h-screen`}
    >
      <Disable />

      <AnimatePresence exitBeforeEnter>
        {step === 1 && (
          <motion.div
            key={step ? step : 'empty'}
            initial={{
              y: 10,
              opacity: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
            }}
            animate={{
              y: 0,
              opacity: 1,
              width: '100%',
              height: '100%',
              display: 'flex',
            }}
            exit={{
              y: -10,
              opacity: 0,
              width: '100%',
              height: '100%',
              display: 'flex',
            }}
            transition={{ duration: 0.4 }}
          >
            <SignupDetails
              signUpData={signUpData}
              setSignUpData={setSignUpData}
              step={step}
              setStep={setStep}
            />
          </motion.div>
        )}
        {step === 2 && (
          <PrimaryGoal
            signUpData={signUpData}
            setSignUpData={setSignUpData}
            step={step}
            setStep={setStep}
          />
        )}
        {step === 3 && (
          <ProjectDetails
            signUpData={signUpData}
            setSignUpData={setSignUpData}
            step={step}
            setStep={setStep}
          />
        )}
        {step === 4 && (
          <BusinessDetails
            signUpData={signUpData}
            setSignUpData={setSignUpData}
            step={step}
            setStep={setStep}
          />
        )}

        {step === 5 && (
          <OnboardingCall
            signUpData={signUpData}
            step={step}
            setStep={setStep}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default SignupNew;
