'use client';
import useAuth from '@/Auth/useAuth';
import Disable from '@/components/DisableDevtool';
import api from '@/lib/api';
import logo from '@/public/Light_Green_For_Dark_BG.png';
import log_in from '@/public/images/book_demo_signup.png';
import styles from '@/styles/Signup.module.css';
import { errorToast, successToast } from '@/utils/toast';
import { useMutation } from '@tanstack/react-query';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { BsCheck2Circle } from 'react-icons/bs';
import { isValidPhoneNumber } from 'react-phone-number-input';
import Input from 'react-phone-number-input/input';
const Index = () => {
  const router = useRouter();
  const { user, loading } = useAuth();
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm();

  useEffect(() => {
    if (user && !loading) {
      router.push('/');
    }
  }, [user, loading, router]);

  const onSubmit = (data) => {
    errorToast('Will be open soon!');
    const a: HTMLLinkElement = document.createElement(
      'a'
    ) as unknown as HTMLLinkElement;
    a.target = '_blank';
    a.href = 'https://standard-insights.com/thank-you-demo/';
    a.click();
    return;
  };

  const mutateBookDemo = useMutation({
    mutationFn: async (data: any) => {
      const res = await api.post('/book-demo', { ...data });
      return res.data;
    },
    onSuccess: () => {
      successToast('Scheduled!');
    },
    onError: (error: any) => {
      errorToast(
        error?.response?.data?.message
          ? error?.response?.data?.message
          : error.message
      );
    },
    retry: 3,
  });

  useEffect(() => {
    if (typeof window !== undefined) {
      document.title = 'Request a Demo - Standard Insights';
    }
  }, []);

  return (
    <div
      className={`${styles.signup__wrapper} relative flex justify-center  w-full mx-auto h-screen`}
    >
      <Disable />

      {/* for screen larger than 991px  */}
      <div className="lg:flex bg-[#0E4449] w-full h-full hidden">
        <div className="flex items-center justify-center w-7/12 text-center text-white">
          <div>
            <h3>
              Get a Personalized 30-minute demo
              <br /> of Standard Insights
            </h3>
            <p className="font-normal">
              Get a live demo and have your questions answered by one of our
              team experts.
            </p>
            <Image
              src={log_in}
              alt="Image"
              className="w-full max-w-xl mx-auto my-5 h-80"
            />
            <div>
              <ul className="flex flex-col items-center justify-center gap-3 text-sm">
                <li className="flex items-center gap-2">
                  <BsCheck2Circle className="text-secondary" />{' '}
                  <span className="font-normal">
                    Capabilities and methodology, and why our data is better.
                  </span>
                </li>
                <li className="flex items-center gap-2">
                  <BsCheck2Circle className="text-secondary" />{' '}
                  <span className="font-normal">Platform overview.</span>
                </li>
                <li className="flex items-center gap-2">
                  <BsCheck2Circle className="text-secondary" />{' '}
                  <span className="font-normal">
                    Data visualization or in-depth analysis of your data.
                  </span>
                </li>
                <li className="flex items-center gap-2">
                  <BsCheck2Circle className="text-secondary" />{' '}
                  <span className="font-normal">
                    Free account plus access to our public insights.
                  </span>
                </li>
              </ul>
            </div>
            <div className="h-[65%] w-full relative my-3">
              <div className="absolute top-0 left-0 z-0 flex items-center justify-center w-full h-full"></div>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-center w-5/12 h-full overflow-y-scroll bg-white">
          <div className="max-w-[440px] w-full mt-28 mb-10 h-auto">
            <Image
              src={logo}
              alt="Image"
              width={360}
              height={66}
              className="h-[33px] w-[180px]"
            />
            <h3 className="my-10 font-bold text-primary">
              Schedule a Free Demo
            </h3>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 gap-3 md:grid-cols-1">
                <div className="grid grid-cols-2 gap-3">
                  <div className="w-full">
                    <label
                      htmlFor="firstName"
                      className="block mb-1 body-2 text-black-3 caption"
                    >
                      First Name*
                    </label>
                    <input
                      id="firstName"
                      type="text"
                      placeholder="Enter first name"
                      className={
                        errors?.firstName
                          ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                          : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                      }
                      {...register('firstName', {
                        required: {
                          value: true,
                          message: 'First name is required',
                        },
                      })}
                    />
                    {errors?.firstName && (
                      <label className="pt-1 pb-0 label text-error">
                        <span className="label-text-alt text-error">
                          {errors?.firstName?.message as string}
                        </span>
                      </label>
                    )}
                  </div>
                  <div className="w-full">
                    <label
                      htmlFor="lastName"
                      className="block mb-1 body-2 text-black-3 caption "
                    >
                      Last Name*
                    </label>
                    <input
                      id="lastName"
                      type="text"
                      placeholder="Enter last name"
                      className={
                        errors?.lastName
                          ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                          : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                      }
                      {...register('lastName', {
                        required: {
                          value: true,
                          message: 'Last name is required',
                        },
                      })}
                    />
                    {errors?.lastName && (
                      <label className="pt-1 pb-0 label text-error">
                        <span className="label-text-alt text-error">
                          {errors.lastName.message as string}
                        </span>
                      </label>
                    )}
                  </div>
                </div>
                <div className="w-full">
                  <label
                    htmlFor="email"
                    className="block mb-1 body-2 text-black-3 caption "
                  >
                    Company Email*
                  </label>
                  <input
                    id="email"
                    type="email"
                    placeholder="Enter your company email"
                    className={
                      errors?.email
                        ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                        : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                    }
                    {...register('email', {
                      required: {
                        value: true,
                        message: 'Email is required',
                      },
                      pattern: {
                        value: /[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$/,
                        message: 'Invalid email address',
                      },
                    })}
                  />
                  {errors?.email && (
                    <label className="pt-1 pb-0 label text-error">
                      <span className="label-text-alt text-error">
                        {errors.email.message as string}
                      </span>
                    </label>
                  )}
                </div>
                <div className="w-full">
                  <label
                    htmlFor="companyName"
                    className="block mb-1 body-2 text-black-3 caption "
                  >
                    Company*
                  </label>
                  <input
                    id="companyName"
                    type="text"
                    placeholder="Enter Company Name"
                    className={
                      errors?.companyName
                        ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                        : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                    }
                    {...register('companyName', {
                      required: {
                        value: true,
                        message: 'Company name is required',
                      },
                    })}
                  />
                  {errors?.companyName && (
                    <label className="pt-1 pb-0 label text-error">
                      <span className="label-text-alt text-error">
                        {errors.companyName.message as string}
                      </span>
                    </label>
                  )}
                </div>
                <div className="w-full">
                  <label
                    htmlFor="position"
                    className="block mb-1 body-2 text-black-3 caption "
                  >
                    Position*
                  </label>
                  <input
                    id="position"
                    type="text"
                    placeholder="Enter position"
                    className={
                      errors?.position
                        ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                        : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                    }
                    {...register('position', {
                      required: {
                        value: true,
                        message: 'Position is required',
                      },
                    })}
                  />
                  {errors?.position && (
                    <label className="pt-1 pb-0 label text-error">
                      <span className="label-text-alt text-error">
                        {errors.position.message as string}
                      </span>
                    </label>
                  )}
                </div>
                <div className="w-full">
                  <label
                    htmlFor="phone"
                    className="block mb-1 body-2 text-black-3 caption "
                  >
                    Phone*
                  </label>
                  <Controller
                    name="phone"
                    control={control}
                    rules={{
                      validate: (value) =>
                        value
                          ? isValidPhoneNumber(value)
                            ? true
                            : 'Invalid phone number'
                          : 'Phone number required',
                    }}
                    render={({ field: { onChange, value } }) => (
                      <Input
                        placeholder="Enter phone number"
                        value={value}
                        name="phone"
                        onChange={onChange}
                        id="phone"
                        className={
                          errors?.phone
                            ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                            : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                        }
                      />
                    )}
                  />
                  {errors?.phone && (
                    <label className="pt-1 pb-0 label text-error">
                      <span className="label-text-alt text-error">
                        {errors.phone.message as string}
                      </span>
                    </label>
                  )}
                </div>
                <div>
                  <button
                    type="submit"
                    className="w-full max-w-sm mx-auto mt-10 text-base font-bold text-center shadow-none btn__lg h-9"
                  >
                    Book a demo call
                  </button>
                  <p className="mt-2 text-xs text-center text-black-3">
                    <span>
                      @ 2023 Standard Insights, All rights reserved. |{' '}
                    </span>
                    <a
                      target="_blank"
                      href={
                        'https://standard-insights.com/terms-and-conditions'
                      }
                      className="duration-300 text-secondary hover:text-primary"
                    >
                      Terms & Conditions
                    </a>
                    {' | '}
                    <a
                      target="_blank"
                      href={'https://standard-insights.com/privacy-policy/'}
                      className="duration-300 text-secondary hover:text-primary"
                    >
                      Privacy Policy
                    </a>
                  </p>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
      {/* for screens larger than 768px and smaller than 992px  */}
      <div className="flex justify-center items-center lg:hidden bg-[#0E4449] w-full overflow-y-scroll">
        <div className="flex items-center justify-center w-10/12 p-3 bg-white sm:w-8/12 md:w-6/12 rounded-2xl">
          <div className="max-w-[440px] w-full p-5 pb-3 h-auto">
            <Image
              src={logo}
              alt="Image"
              width={360}
              height={66}
              className="h-[33px] w-[180px]"
            />
            <h4 className="my-3 font-bold text-primary sm:h4 h5">
              Schedule a Free Demo
            </h4>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid grid-cols-1 gap-3 md:grid-cols-1">
                <div className="grid grid-cols-2 gap-3">
                  <div className="w-full">
                    <label
                      htmlFor="firstName"
                      className="block mb-1 body-2 text-black-3 caption"
                    >
                      First Name*
                    </label>
                    <input
                      id="firstName"
                      type="text"
                      placeholder="Enter first name"
                      className={
                        errors?.firstName
                          ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                          : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                      }
                      {...register('firstName', {
                        required: {
                          value: true,
                          message: 'First name is required',
                        },
                      })}
                    />
                    {errors?.firstName && (
                      <label className="pt-1 pb-0 label text-error">
                        <span className="label-text-alt text-error">
                          {errors.firstName.message as string}
                        </span>
                      </label>
                    )}
                  </div>
                  <div className="w-full">
                    <label
                      htmlFor="lastName"
                      className="block mb-1 body-2 text-black-3 caption "
                    >
                      Last Name*
                    </label>
                    <input
                      id="lastName"
                      type="text"
                      placeholder="Enter last name"
                      className={
                        errors?.lastName
                          ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                          : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                      }
                      {...register('lastName', {
                        required: {
                          value: true,
                          message: 'Last name is required',
                        },
                      })}
                    />
                    {errors?.lastName && (
                      <label className="pt-1 pb-0 label text-error">
                        <span className="label-text-alt text-error">
                          {errors.lastName.message as string}
                        </span>
                      </label>
                    )}
                  </div>
                </div>
                <div className="w-full">
                  <label
                    htmlFor="email"
                    className="block mb-1 body-2 text-black-3 caption "
                  >
                    Company Email*
                  </label>
                  <input
                    id="email"
                    type="email"
                    placeholder="Enter your company email"
                    className={
                      errors?.email
                        ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                        : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                    }
                    {...register('email', {
                      required: {
                        value: true,
                        message: 'Email is required',
                      },
                      pattern: {
                        value: /[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$/,
                        message: 'Invalid email address',
                      },
                    })}
                  />
                  {errors?.email && (
                    <label className="pt-1 pb-0 label text-error">
                      <span className="label-text-alt text-error">
                        {errors.email.message as string}
                      </span>
                    </label>
                  )}
                </div>
                <div className="w-full">
                  <label
                    htmlFor="companyName"
                    className="block mb-1 body-2 text-black-3 caption "
                  >
                    Company*
                  </label>
                  <input
                    id="companyName"
                    type="text"
                    placeholder="Enter Company Name"
                    className={
                      errors?.companyName
                        ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                        : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                    }
                    {...register('companyName', {
                      required: {
                        value: true,
                        message: 'Company name is required',
                      },
                    })}
                  />
                  {errors?.companyName && (
                    <label className="pt-1 pb-0 label text-error">
                      <span className="label-text-alt text-error">
                        {errors.companyName.message as string}
                      </span>
                    </label>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div className="w-full">
                    <label
                      htmlFor="position"
                      className="block mb-1 body-2 text-black-3 caption "
                    >
                      Position*
                    </label>
                    <input
                      id="position"
                      type="text"
                      placeholder="Enter position"
                      className={
                        errors?.position
                          ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                          : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                      }
                      {...register('position', {
                        required: {
                          value: true,
                          message: 'Position is required',
                        },
                      })}
                    />
                    {errors?.position && (
                      <label className="pt-1 pb-0 label text-error">
                        <span className="label-text-alt text-error">
                          {errors.position.message as string}
                        </span>
                      </label>
                    )}
                  </div>
                  <div className="w-full">
                    <label
                      htmlFor="phone"
                      className="block mb-1 body-2 text-black-3 caption "
                    >
                      Phone*
                    </label>
                    <Controller
                      name="phone"
                      control={control}
                      rules={{
                        validate: (value) =>
                          value
                            ? isValidPhoneNumber(value)
                              ? true
                              : 'Invalid phone number'
                            : 'Phone number required',
                      }}
                      render={({ field: { onChange, value } }) => (
                        <Input
                          placeholder="Enter phone number"
                          value={value}
                          name="phone"
                          onChange={onChange}
                          id="phone"
                          className={
                            errors?.phone
                              ? 'h-[38px] focus:outline-none bg-white border border-error rounded-md px-2 w-full pr-8 subtitle-2 text-sm font-normal'
                              : 'h-[38px] focus:outline-none bg-white border border-[#8692A6] rounded-md px-2 w-full subtitle-2 text-sm font-normal'
                          }
                        />
                      )}
                    />
                    {errors?.phone && (
                      <label className="pt-1 pb-0 label text-error">
                        <span className="label-text-alt text-error">
                          {errors.phone.message as string}
                        </span>
                      </label>
                    )}
                  </div>
                </div>
                <div>
                  <button
                    type="submit"
                    className="w-full max-w-sm mx-auto mt-5 text-base font-bold text-center shadow-none btn__lg h-9"
                  >
                    Book a demo call
                  </button>
                  <p className="mt-2 text-xs text-center text-black-3">
                    <span>
                      @ 2023 Standard Insights, All rights reserved. |{' '}
                    </span>
                    <a
                      target="_blank"
                      href={
                        'https://standard-insights.com/terms-and-conditions'
                      }
                      className="duration-300 text-secondary hover:text-primary"
                    >
                      Terms & Conditions
                    </a>
                    {' | '}
                    <a
                      target="_blank"
                      href={'https://standard-insights.com/privacy-policy/'}
                      className="duration-300 text-secondary hover:text-primary"
                    >
                      Privacy Policy
                    </a>
                  </p>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
