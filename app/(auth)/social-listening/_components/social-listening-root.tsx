/* eslint-disable @typescript-eslint/ban-ts-comment */
//@ts-nocheck
'use client';
import useAuth from '@/Auth/useAuth';
import DefaultLayout from '@/layout/DefaultLayout';
import api from '@/lib/api';
import qualitative_screen from '@/public/images/social_listening.png';
import { errorToast, successToast } from '@/utils/toast';
import { useMutation } from '@tanstack/react-query';
import Image from 'next/image';
import { useEffect } from 'react';
import { BsCurrencyDollar } from 'react-icons/bs';

const SocialListeningRoot = () => {
  const { dbUser, team, setNewTeam } = useAuth();
  const mutateRequestSocial = useMutation({
    mutationFn: async () => {
      const res = await api.post(`/team/social-listening/${team._id}`, {
        user: {
          name: dbUser.firstName + ' ' + dbUser.lastName,
          email: dbUser.email,
          _id: dbUser._id,
        },
        team: { companyName: team.companyName, _id: team._id },
      });
      return res.data.data;
    },
    onSuccess: (data) => {
      setNewTeam(data);
      successToast('Social Listenting Request Sent!');
    },
    onError: (error: any) => {
      errorToast(error?.response?.data?.message || error?.message);
    },
    retry: 3,
  });

  useEffect(() => {
    if (typeof window !== undefined) {
      document.title = 'Social Listening';
    }
  }, []);

  return (
    <DefaultLayout>
      <div className="page__wrapper bg-[#F0F6F6] h-screen overflow-y-auto flex justify-center items-center">
        <div className="w-full min-h-[350px] flex justify-center items-center pb-4">
          <div>
            <h3 className="text-center text-secondary">
              Unlock the Power of Social Listening
            </h3>
            <div className="flex justify-center items-center mx-auto lg:max-w-[750px] xl:max-w-[850px] 2xl:max-w-[950px] py-4">
              <Image
                src={qualitative_screen}
                alt="imaged"
                width={1285}
                height={900}
                className="inline-block object-cover mx-auto"
              />
            </div>
            <p className="text-center text-base max-w-[800px] mx-auto text-primary">
              Harness the full potential of social media with our Social
              Listening tool. Connect your existing solutions and dive deep into
              the world of social analytics to strategically position your brand
              in the digital landscape. Understand the buzz around your products
              and respond swiftly to shape your brand perception effectively.
            </p>
            <div className="flex items-center justify-center w-full my-5">
              {/* <RequestPersona /> */}
              <button
                onClick={() => mutateRequestSocial.mutate()}
                disabled={
                  mutateRequestSocial.isLoading || team?.isSocialListenRequested
                }
                className="px-10 text-white shadow-none btn__lg bg-secondary rounded-2xl"
              >
                <BsCurrencyDollar className="mr-4 text-xs bg-white rounded-full btn__sm__icon text-secondary" />
                <span>Activate Social Listening Now</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </DefaultLayout>
  );
};

export default SocialListeningRoot;
