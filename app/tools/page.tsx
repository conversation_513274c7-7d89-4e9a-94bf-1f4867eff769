'use client';
import SmallLoading from '@/components/Loading/SmallLoading';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import dynamic from 'next/dynamic';
import { Suspense, useEffect } from 'react';
import { MdOutlineWifiTetheringErrorRounded } from 'react-icons/md';
import { TbUsersGroup } from 'react-icons/tb';

const DynamicMarginOfErr = dynamic(
  () => import('@/components/MarginOfError/MarginOfErr'),
  {
    suspense: true,
  }
);
const DynamicSampleSize = dynamic(
  () => import('@/components/MarginOfError/SampleSize'),
  {
    suspense: true,
  }
);

const MarginOfError = () => {
  useEffect(() => {
    if (typeof window !== undefined) {
      document.title = 'Margin Of Error';
    }
  }, []);

  // <DefaultLayout>
  //   </DefaultLayout>
  return (
    <div className="page__wrapper p-10 #bg-[#F8F9FC]">
      <div>
        <h3 className="mb-5 text-primary h4 lg:mb-0 ">Tools</h3>
      </div>
      <Tabs defaultValue="margin" className="mt-5">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="margin">
            <MdOutlineWifiTetheringErrorRounded className="mr-1" />
            <span> Margin of error</span>
          </TabsTrigger>
          <TabsTrigger value="sample">
            <TbUsersGroup className="mr-1" />
            <span>Sample size</span>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="margin">
          <div className="py-3 mb-7">
            <h3 className="text-center text-secondary">
              Margin of Error Calculator
            </h3>
            <p className="max-w-lg mx-auto mt-3 text-center">
              Determine the precision of your survey findings with this margin
              of error calculator. Enter key survey values below, and we will
              compute the margin of error.
            </p>
          </div>
          <Suspense
            fallback={
              <div className="flex items-center justify-center w-full h-full">
                <SmallLoading />
              </div>
            }
          >
            <DynamicMarginOfErr />
          </Suspense>
        </TabsContent>
        <TabsContent value="sample">
          <div className="py-3 mb-7">
            <h3 className="text-center text-secondary">
              Sample Size Calculator
            </h3>
            <p className="max-w-lg mx-auto mt-3 text-center">
              Determine a representative survey sample size for your project.
              Enter key survey values below, and we will compute the sample
              size.
            </p>
          </div>
          <Suspense
            fallback={
              <div className="flex items-center justify-center w-full h-full">
                <SmallLoading />
              </div>
            }
          >
            <DynamicSampleSize />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MarginOfError;
