/* eslint-disable react/no-unescaped-entities */
'use client';

import type React from 'react';

import { Eye, EyeOff, Lock, Mail } from 'lucide-react';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import axios from '@/utils/axios';
import { errorToast, successToast } from '@/utils/toast';
import { sendGAEvent } from '@next/third-parties/google';
import { ArrowRightIcon } from '@phosphor-icons/react';
import { useMutation } from '@tanstack/react-query';
import { useDebounce } from '@uidotdev/usehooks';
import type { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';

const freeEmailDomains = [
  'gmail.com',
  'yahoo.com',
  'hotmail.com',
  'outlook.com',
  'aol.com',
  'icloud.com',
  'mail.com',
  'zoho.com',
  'protonmail.com',
  'gmx.com',
  'yandex.com',
  'live.com',
  'msn.com',
  'micamail.in',
];
function isFreeEmail(email: string) {
  const domain = email.split('@')[1]?.toLowerCase();
  return freeEmailDomains.includes(domain);
}

export default function SignUpForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [emailError, setEmailError] = useState('');
  // const [isEmailChecking, setIsEmailChecking] = useState(false);
  const [loading, setLoading] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [firstNameError, setFirstNameError] = useState(false);
  const [lastNameError, setLastNameError] = useState(false);
  const [companyNameError, setCompanyNameError] = useState(false);
  const router = useRouter();
  const [hearAboutUs, setHearAboutUs] = useState('');
  const [hearAboutUsDetails, setHearAboutUsDetails] = useState('');
  const [passwordRequirements, setPasswordRequirements] = useState({
    length: false,
    lowercase: false,
    uppercase: false,
    number: false,
    special: false,
  });
  const [showTooltip, setShowTooltip] = useState(false);
  //Debounce
  const [email, setEmail] = useState('');
  const debouncedEmail = useDebounce(email, 1000);
  const [password, setPassword] = useState('');

  const { mutate, isLoading: isEmailChecking } = useMutation({
    mutationFn: async ({ email }: { email: string }) => {
      const response = await axios.post('/validate-email', { email });
      return response;
    },
    onSuccess: (data) => {
      if (data.status === 200) {
        setEmailError('');
      } else {
        setEmailError('Please use a valid professional email address');
      }
    },
    onError: () => {
      setEmailError('Please use a valid professional email address');
    },
    retry: 0,
  });

  useEffect(() => {
    if (
      isFreeEmail(debouncedEmail) &&
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(debouncedEmail)
    ) {
      return setEmailError('Please use a professional email');
    } else if (
      debouncedEmail.length !== 0 &&
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(debouncedEmail)
    ) {
      mutate({ email });
    }
  }, [debouncedEmail]);

  const validatePassword = (password: string) => {
    setPassword(password);
    const requirements = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /[0-9]/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    setPasswordRequirements(requirements);

    if (!password) {
      setPasswordError('Password is required');
      return false;
    }

    const isValid = Object.values(requirements).every(Boolean);
    setPasswordError(isValid ? '' : 'Please meet all password requirements');
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // const password = password;
    const email = debouncedEmail;
    if (!email || !validatePassword(password)) {
      setEmailError(email ? '' : 'Email is required');
      validatePassword(password);
      return;
    }
    localStorage.setItem('signup-email', debouncedEmail);
    localStorage.setItem('signup-password', password);
    setLoading(true);
    console.log('Form submitted');
    // Handle form submission
    try {
      const user = await axios.post('/users/signup-with-create-workspace', {
        email: debouncedEmail,
        pass: password,
      });
      // console.log(user);
      if (user.status === 200) {
        successToast('User created successfully! Please verify your email');
        sendGAEvent('event', 'signup_account_created', {
          email,
          id: user.data.data._id,
        });
        router.push(
          '/signup/email-verification?email=' +
            user.data.data.email +
            '&_id=' +
            user.data.data._id
        );
      } else if (user.status === 209) {
        errorToast('User already exists');
      }
    } catch (error: AxiosError | any) {
      if (error && error.status! === 409) {
        errorToast('User already exists. Please login');
      } else {
        errorToast('Something went wrong');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="mx-auto w-full max-w-[600px] lg:w-[85%] lg:max-w-[800px] space-y-6 p-6 py-10 lg:py-0 lg:p-24">
      <h1 className="text-2xl xl:text-3xl d2xl:text-4xl font-semibold text-center text-gray-600">
        Get Started For Free
      </h1>

      <form className="mt-8" onSubmit={handleSubmit}>
        <div className="space-y-1 mb-6">
          <Label htmlFor="email" className="p3-medium text-gray-500">
            Work Email
          </Label>
          <div className="relative">
            <Mail className="absolute w-4 h-4 text-gray-400 left-3 top-3" />
            <Input
              id="email"
              type="email"
              placeholder="Work Email"
              className={`pl-10 h-[41px] ${
                emailError ? 'border-red-400 focus-visible:ring-red-400' : ''
              }`}
              onChange={(e) => setEmail(e.target.value)}
            />
            {isEmailChecking && (
              <p className="mt-1 text-sm text-gray-500">Checking email...</p>
            )}
            {emailError && !isEmailChecking && (
              <p className="mt-1 text-sm text-red-500">{emailError}</p>
            )}
          </div>
        </div>

        <div className="mt-2 space-y-1">
          <Label htmlFor="password" className="p3-medium text-gray-500">
            Password
          </Label>
          <div className="relative">
            <Lock className="absolute w-4 h-4 text-gray-400 left-3 top-3" />
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              placeholder="Password"
              className={`pl-10 pr-10 h-[41px] ${
                passwordError ? 'border-red-400 focus-visible:ring-red-400' : ''
              }`}
              onChange={(e) => validatePassword(e.target.value)}
              onFocus={() => setShowTooltip(true)}
              onBlur={() => setShowTooltip(false)}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute text-gray-400 right-3 top-3 hover:text-gray-600"
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? (
                <EyeOff className="w-4 h-4" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
            </button>
          </div>
          {passwordError && (
            <p className="mt-1 text-sm text-red-500">{passwordError}</p>
          )}
          {showTooltip && (
            <div className="bottom-full left-0 mb-2  rounded-md bg-white p-4 text-sm shadow-lg border border-gray-200">
              <div className="flex justify-between items-center mb-2">
                <div className="text-gray-700 font-medium">Strength</div>
                <div className="text-gray-700 font-medium">
                  {(() => {
                    const metRequirements =
                      Object.values(passwordRequirements).filter(
                        Boolean
                      ).length;
                    if (metRequirements >= 5) return 'Strong';
                    if (metRequirements >= 3) return 'Medium';
                    return 'Weak';
                  })()}
                </div>
              </div>

              <div className="w-full h-2 bg-gray-200 rounded-full mb-4 overflow-hidden">
                <div
                  className={`h-full ${(() => {
                    const metRequirements =
                      Object.values(passwordRequirements).filter(
                        Boolean
                      ).length;
                    if (metRequirements >= 5) return 'bg-green-500 w-full';
                    if (metRequirements >= 3) return 'bg-yellow-400 w-1/2';
                    return 'bg-red-500 w-1/4';
                  })()}`}
                ></div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-gray-700">
                  <div
                    className={`flex items-center justify-center w-5 h-5 rounded-full ${
                      passwordRequirements.length
                        ? 'bg-green-100 text-green-500'
                        : 'bg-gray-200 text-gray-400'
                    }`}
                  >
                    {passwordRequirements.length && (
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10 3L4.5 8.5L2 6"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    )}
                  </div>
                  <span>8+ characters</span>
                </div>

                <div className="flex items-center gap-2 text-gray-700">
                  <div
                    className={`flex items-center justify-center w-5 h-5 rounded-full ${
                      passwordRequirements.lowercase &&
                      passwordRequirements.uppercase
                        ? 'bg-green-100 text-green-500'
                        : 'bg-gray-200 text-gray-400'
                    }`}
                  >
                    {passwordRequirements.lowercase &&
                    passwordRequirements.uppercase ? (
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10 3L4.5 8.5L2 6"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    ) : null}
                  </div>
                  <span>Upper case and lower case</span>
                </div>

                <div className="flex items-center gap-2 text-gray-700">
                  <div
                    className={`flex items-center justify-center w-5 h-5 rounded-full ${
                      passwordRequirements.number &&
                      passwordRequirements.special
                        ? 'bg-green-100 text-green-500'
                        : 'bg-gray-200 text-gray-400'
                    }`}
                  >
                    {passwordRequirements.number &&
                    passwordRequirements.special ? (
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M10 3L4.5 8.5L2 6"
                          stroke="currentColor"
                          strokeWidth="1.5"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    ) : null}
                  </div>
                  <span>Number and special characters</span>
                </div>
              </div>
            </div>
          )}
        </div>

        <Button
          type="submit"
          disabled={
            loading ||
            isEmailChecking ||
            !!emailError ||
            !validatePassword ||
            !email ||
            !password
          }
          className="w-full text-white bg-secondary hover:bg-primary-600 disabled:cursor-not-allowed mt-8"
        >
          {/* {loading ? 'Loading...' : 'Continue'} */}
          {loading ? 'Loading...' : 'Create your free account'}
          <ArrowRightIcon className="ml-2" />
        </Button>
        {/* <AuthButtons /> */}
        <p className="text-sm text-center text-gray-600 mt-8">
          Already have an account?{' '}
          <Link
            shallow={true}
            href="/login"
            className="text-secondary hover:text-primary-600 font-bold"
          >
            Sign In
          </Link>
        </p>
      </form>
    </div>
  );
}
